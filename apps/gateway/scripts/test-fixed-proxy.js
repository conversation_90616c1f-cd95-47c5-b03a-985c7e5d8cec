#!/usr/bin/env node

/**
 * 修复后的Gateway HTTP代理测试脚本
 * 基于migration分支的正确路径格式
 */

const axios = require('axios');

// 配置
const GATEWAY_URL = 'http://127.0.0.1:3000';
const AUTH_DIRECT_URL = 'http://127.0.0.1:3001';

// 创建axios实例
const client = axios.create({
  timeout: 10000, // 10秒超时
  headers: {
    'Content-Type': 'application/json',
  },
});

/**
 * 发送HTTP请求
 */
async function makeRequest(method, url, data = null) {
  try {
    console.log(`🔄 ${method} ${url}`);
    if (data) {
      console.log(`📤 请求数据:`, JSON.stringify(data, null, 2));
    }
    
    const response = await client({
      method,
      url,
      data,
    });
    
    console.log(`✅ 响应状态: ${response.status}`);
    console.log(`📥 响应数据:`, JSON.stringify(response.data, null, 2));
    return response;
  } catch (error) {
    if (error.response) {
      console.log(`❌ 响应错误: ${error.response.status}`);
      console.log(`📥 错误数据:`, JSON.stringify(error.response.data, null, 2));
    } else if (error.request) {
      console.log(`❌ 请求失败: ${error.message}`);
    } else {
      console.log(`❌ 配置错误: ${error.message}`);
    }
    throw error;
  }
}

/**
 * 主测试函数
 */
async function runFixedProxyTest() {
  console.log('🚀 开始修复后的Gateway HTTP代理测试');
  console.log(`📍 Gateway地址: ${GATEWAY_URL}`);
  console.log(`📍 Auth直连地址: ${AUTH_DIRECT_URL}`);
  
  const timestamp = Date.now();
  const testUser = {
    username: `user${timestamp}_proxy`,
    email: `user${timestamp}<EMAIL>`,
    password: 'Test123456!',
  };
  
  try {
    // 测试Gateway健康检查
    console.log('\n🧪 ========== 测试Gateway健康状态 ==========\n');
    await makeRequest('GET', `${GATEWAY_URL}/health`);
    
    // 测试Auth服务健康检查（通过Gateway代理）
    console.log('\n🧪 ========== 测试Auth服务健康检查（代理） ==========\n');
    await makeRequest('GET', `${GATEWAY_URL}/api/auth/health`);
    
    // 测试用户注册（通过Gateway代理）
    console.log('\n🧪 ========== 测试用户注册（代理） ==========\n');
    const registerResponse = await makeRequest('POST', `${GATEWAY_URL}/api/auth/auth/register`, testUser);
    
    // 测试用户登录（通过Gateway代理）
    console.log('\n🧪 ========== 测试用户登录（代理） ==========\n');
    const loginData = {
      username: testUser.username,
      password: testUser.password,
    };
    const loginResponse = await makeRequest('POST', `${GATEWAY_URL}/api/auth/auth/login`, loginData);
    
    console.log('\n🎉 所有代理测试通过！Gateway HTTP代理工作正常');
    
  } catch (error) {
    console.log('\n❌ 代理测试失败');
    console.log('错误详情:', error.message);
    process.exit(1);
  }
}

// 运行测试
runFixedProxyTest().catch(console.error);
