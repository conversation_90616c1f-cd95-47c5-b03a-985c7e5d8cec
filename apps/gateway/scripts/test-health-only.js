#!/usr/bin/env node

/**
 * 简单健康检查测试脚本
 * 用于验证Gateway是否正常启动
 */

const axios = require('axios');

// 配置
const GATEWAY_URL = 'http://127.0.0.1:3000';

// 创建axios实例
const client = axios.create({
  timeout: 5000, // 5秒超时
  headers: {
    'Content-Type': 'application/json',
  },
});

/**
 * 发送HTTP请求
 */
async function makeRequest(method, url) {
  try {
    console.log(`🔄 ${method} ${url}`);
    
    const response = await client({
      method,
      url,
    });
    
    console.log(`✅ 响应状态: ${response.status}`);
    return response;
  } catch (error) {
    if (error.response) {
      console.log(`❌ 响应错误: ${error.response.status}`);
    } else if (error.request) {
      console.log(`❌ 请求失败: ${error.message}`);
    } else {
      console.log(`❌ 配置错误: ${error.message}`);
    }
    throw error;
  }
}

/**
 * 主测试函数
 */
async function runHealthTest() {
  console.log('🚀 开始Gateway健康检查测试');
  console.log(`📍 Gateway地址: ${GATEWAY_URL}`);
  
  try {
    // 测试Gateway健康检查
    console.log('\n🧪 ========== 测试Gateway健康状态 ==========\n');
    await makeRequest('GET', `${GATEWAY_URL}/health`);
    console.log('✅ Gateway健康检查通过');
    
    console.log('\n🎉 健康检查测试完成！Gateway正常运行');
    
  } catch (error) {
    console.log('\n❌ 健康检查测试失败');
    console.log('错误详情:', error.message);
    process.exit(1);
  }
}

// 运行测试
runHealthTest().catch(console.error);
