/**
 * Gateway HTTP代理测试脚本
 * 
 * 测试Gateway的HTTP代理功能，验证路由转发是否正常工作
 * 
 * 代理机制：
 * 1. 客户端请求: POST http://localhost:3000/api/auth/login
 * 2. Gateway路由匹配: /api/auth/* -> auth服务
 * 3. 路径重写: /api/auth/login -> /auth/login (移除/api/auth前缀)
 * 4. 代理转发: http://localhost:3001/auth/login
 */

const axios = require('axios');

// 配置
const GATEWAY_URL = 'http://127.0.0.1:3000';
const AUTH_SERVICE_URL = 'http://127.0.0.1:3001';
const TEST_USER = {
  username: `test_proxy_${Date.now()}`,
  email: `test_proxy_${Date.now()}@example.com`,
  password: 'TestProxy123!',
  firstName: 'Test',
  lastName: 'Proxy'
};

// 测试结果存储
const testResults = {
  gatewayHealth: null,
  authServiceHealth: null,
  directRegister: null,
  proxyRegister: null,
  directLogin: null,
  proxyLogin: null,
  proxyProfile: null
};

/**
 * 发送HTTP请求的通用方法
 */
async function makeRequest(method, url, data = null, headers = {}) {
  try {
    console.log(`\n🔄 ${method.toUpperCase()} ${url}`);
    if (data) {
      console.log(`📤 请求数据:`, JSON.stringify(data, null, 2));
    }

    const config = {
      method,
      url,
      headers: {
        'Content-Type': 'application/json',
        ...headers
      },
      timeout: 10000
    };

    if (data) {
      config.data = data;
    }

    const response = await axios(config);
    
    console.log(`✅ 响应状态: ${response.status}`);
    console.log(`📥 响应数据:`, JSON.stringify(response.data, null, 2));
    
    return {
      success: true,
      status: response.status,
      data: response.data,
      headers: response.headers
    };
  } catch (error) {
    console.log(`❌ 请求失败: ${error.message}`);
    if (error.response) {
      console.log(`📥 错误响应 (${error.response.status}):`, JSON.stringify(error.response.data, null, 2));
      return {
        success: false,
        status: error.response.status,
        data: error.response.data,
        headers: error.response.headers
      };
    }
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * 测试Gateway健康状态
 */
async function testGatewayHealth() {
  console.log('\n🧪 ========== 测试Gateway健康状态 ==========');
  
  const result = await makeRequest('GET', `${GATEWAY_URL}/health`);
  testResults.gatewayHealth = result;
  
  if (result.success) {
    console.log('✅ Gateway健康检查通过');
    return true;
  } else {
    console.log('❌ Gateway健康检查失败');
    return false;
  }
}

/**
 * 测试Auth服务健康状态
 */
async function testAuthServiceHealth() {
  console.log('\n🧪 ========== 测试Auth服务健康状态 ==========');
  
  const result = await makeRequest('GET', `${AUTH_SERVICE_URL}/health`);
  testResults.authServiceHealth = result;
  
  if (result.success) {
    console.log('✅ Auth服务健康检查通过');
    return true;
  } else {
    console.log('❌ Auth服务健康检查失败');
    return false;
  }
}

/**
 * 测试直接调用Auth服务注册
 */
async function testDirectRegister() {
  console.log('\n🧪 ========== 测试直接调用Auth服务注册 ==========');
  
  const result = await makeRequest('POST', `${AUTH_SERVICE_URL}/auth/register`, {
    username: TEST_USER.username + '_direct',
    email: TEST_USER.email.replace('@', '_direct@'),
    password: TEST_USER.password,
    confirmPassword: TEST_USER.password,
    acceptTerms: true,
    profile: {
      firstName: TEST_USER.firstName,
      lastName: TEST_USER.lastName,
      language: 'zh-CN'
    }
  });

  testResults.directRegister = result;
  
  if (result.success) {
    console.log('✅ 直接调用Auth服务注册成功');
    return true;
  } else {
    console.log('❌ 直接调用Auth服务注册失败');
    return false;
  }
}

/**
 * 测试通过Gateway代理注册
 */
async function testProxyRegister() {
  console.log('\n🧪 ========== 测试通过Gateway代理注册 ==========');
  console.log('📍 代理路径: /api/auth/register -> Auth服务 /auth/register');
  
  const result = await makeRequest('POST', `${GATEWAY_URL}/api/auth/register`, {
    username: TEST_USER.username,
    email: TEST_USER.email,
    password: TEST_USER.password,
    profile: {
      firstName: TEST_USER.firstName,
      lastName: TEST_USER.lastName,
      language: 'zh-CN'
    }
  });

  testResults.proxyRegister = result;
  
  if (result.success) {
    console.log('✅ Gateway代理注册成功');
    return true;
  } else {
    console.log('❌ Gateway代理注册失败');
    return false;
  }
}

/**
 * 测试直接调用Auth服务登录
 */
async function testDirectLogin() {
  console.log('\n🧪 ========== 测试直接调用Auth服务登录 ==========');
  
  const result = await makeRequest('POST', `${AUTH_SERVICE_URL}/auth/login`, {
    identifier: TEST_USER.username + '_direct',
    password: TEST_USER.password,
    deviceInfo: {
      type: 'web',
      name: 'Direct Test Client',
      fingerprint: 'direct_test_001',
      userAgent: 'Node.js Test Script',
      ipAddress: '127.0.0.1'
    }
  });

  testResults.directLogin = result;
  
  if (result.success && result.data.accessToken) {
    console.log('✅ 直接调用Auth服务登录成功');
    console.log(`🔑 访问令牌: ${result.data.accessToken.substring(0, 20)}...`);
    return result.data.accessToken;
  } else {
    console.log('❌ 直接调用Auth服务登录失败');
    return null;
  }
}

/**
 * 测试通过Gateway代理登录
 */
async function testProxyLogin() {
  console.log('\n🧪 ========== 测试通过Gateway代理登录 ==========');
  console.log('📍 代理路径: /api/auth/login -> Auth服务 /auth/login');
  
  const result = await makeRequest('POST', `${GATEWAY_URL}/api/auth/login`, {
    identifier: TEST_USER.username,
    password: TEST_USER.password,
    deviceInfo: {
      type: 'web',
      name: 'Gateway Proxy Test Client',
      fingerprint: 'proxy_test_001',
      userAgent: 'Node.js Test Script',
      ipAddress: '127.0.0.1'
    }
  });

  testResults.proxyLogin = result;
  
  if (result.success && result.data.accessToken) {
    console.log('✅ Gateway代理登录成功');
    console.log(`🔑 访问令牌: ${result.data.accessToken.substring(0, 20)}...`);
    return result.data.accessToken;
  } else {
    console.log('❌ Gateway代理登录失败');
    return null;
  }
}

/**
 * 测试通过Gateway代理获取用户信息
 */
async function testProxyProfile(accessToken) {
  console.log('\n🧪 ========== 测试通过Gateway代理获取用户信息 ==========');
  console.log('📍 代理路径: /api/auth/profile -> Auth服务 /auth/profile');
  
  const result = await makeRequest('GET', `${GATEWAY_URL}/api/auth/profile`, null, {
    'Authorization': `Bearer ${accessToken}`
  });

  testResults.proxyProfile = result;
  
  if (result.success) {
    console.log('✅ Gateway代理获取用户信息成功');
    return true;
  } else {
    console.log('❌ Gateway代理获取用户信息失败');
    return false;
  }
}

/**
 * 生成测试报告
 */
function generateTestReport() {
  console.log('\n📊 ========== HTTP代理测试报告 ==========');
  
  const tests = [
    { name: 'Gateway健康检查', result: testResults.gatewayHealth },
    { name: 'Auth服务健康检查', result: testResults.authServiceHealth },
    { name: '直接调用Auth服务注册', result: testResults.directRegister },
    { name: 'Gateway代理注册', result: testResults.proxyRegister },
    { name: '直接调用Auth服务登录', result: testResults.directLogin },
    { name: 'Gateway代理登录', result: testResults.proxyLogin },
    { name: 'Gateway代理获取用户信息', result: testResults.proxyProfile }
  ];

  let passedCount = 0;
  let totalCount = tests.length;

  tests.forEach(test => {
    const status = test.result?.success ? '✅ 通过' : '❌ 失败';
    const statusCode = test.result?.status ? `(${test.result.status})` : '';
    console.log(`${status} ${test.name} ${statusCode}`);
    
    if (test.result?.success) {
      passedCount++;
    }
  });

  console.log(`\n📈 测试统计: ${passedCount}/${totalCount} 通过`);
  console.log(`📈 通过率: ${((passedCount / totalCount) * 100).toFixed(1)}%`);

  // 分析结果
  if (testResults.directRegister?.success && !testResults.proxyRegister?.success) {
    console.log('\n⚠️ 分析：Auth服务正常，但Gateway代理注册失败');
    console.log('   可能原因：路径重写规则不正确或代理配置问题');
  }

  if (testResults.directLogin?.success && !testResults.proxyLogin?.success) {
    console.log('\n⚠️ 分析：Auth服务正常，但Gateway代理登录失败');
    console.log('   可能原因：路径重写规则不正确或代理配置问题');
  }

  if (passedCount === totalCount) {
    console.log('\n🎉 所有测试通过！Gateway HTTP代理功能正常');
  } else {
    console.log('\n⚠️ 部分测试失败，请检查Gateway代理配置');
  }
}

/**
 * 主测试流程
 */
async function runTests() {
  console.log('🚀 开始Gateway HTTP代理测试');
  console.log(`📍 Gateway地址: ${GATEWAY_URL}`);
  console.log(`📍 Auth服务地址: ${AUTH_SERVICE_URL}`);
  console.log(`👤 测试用户: ${TEST_USER.username}`);

  try {
    // 1. 检查Gateway健康状态
    const gatewayOk = await testGatewayHealth();
    if (!gatewayOk) {
      console.log('❌ Gateway不可用，终止测试');
      return;
    }

    // 2. 检查Auth服务健康状态
    const authOk = await testAuthServiceHealth();
    if (!authOk) {
      console.log('❌ Auth服务不可用，终止测试');
      return;
    }

    // 3. 测试直接调用Auth服务注册（对照组）
    await testDirectRegister();

    // 4. 测试Gateway代理注册（实验组）
    const proxyRegisterOk = await testProxyRegister();

    // 5. 测试直接调用Auth服务登录（对照组）
    await testDirectLogin();

    // 6. 测试Gateway代理登录（实验组）
    const accessToken = await testProxyLogin();

    // 7. 如果代理登录成功，测试代理获取用户信息
    if (accessToken) {
      await testProxyProfile(accessToken);
    }

    // 8. 生成测试报告
    generateTestReport();

  } catch (error) {
    console.error('❌ 测试过程中发生异常:', error.message);
  }
}

// 运行测试
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = {
  runTests,
  testResults,
  TEST_USER
};
