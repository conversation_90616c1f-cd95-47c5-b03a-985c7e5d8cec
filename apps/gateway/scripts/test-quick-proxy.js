#!/usr/bin/env node

/**
 * 快速Gateway代理测试脚本
 * 用于验证路径重写修复是否生效
 */

const axios = require('axios');

// 配置
const GATEWAY_URL = 'http://127.0.0.1:3000';
const AUTH_SERVICE_URL = 'http://127.0.0.1:3001';

// 创建axios实例，设置较短的超时时间
const client = axios.create({
  timeout: 10000, // 10秒超时
  headers: {
    'Content-Type': 'application/json',
  },
});

/**
 * 发送HTTP请求
 */
async function makeRequest(method, url, data = null, headers = {}) {
  try {
    const config = {
      method,
      url,
      headers,
    };
    
    if (data) {
      config.data = data;
    }
    
    console.log(`🔄 ${method} ${url}`);
    if (data) {
      console.log('📤 请求数据:', JSON.stringify(data, null, 2));
    }
    
    const response = await client(config);
    
    console.log(`✅ 响应状态: ${response.status}`);
    console.log('📥 响应数据:', JSON.stringify(response.data, null, 2));
    
    return response;
  } catch (error) {
    if (error.response) {
      console.log(`❌ 响应错误: ${error.response.status}`);
      console.log('📥 错误数据:', JSON.stringify(error.response.data, null, 2));
    } else if (error.request) {
      console.log(`❌ 请求失败: ${error.message}`);
    } else {
      console.log(`❌ 配置错误: ${error.message}`);
    }
    throw error;
  }
}

/**
 * 主测试函数
 */
async function runQuickTest() {
  console.log('🚀 开始Gateway快速代理测试');
  console.log(`📍 Gateway地址: ${GATEWAY_URL}`);
  console.log(`📍 Auth服务地址: ${AUTH_SERVICE_URL}`);
  
  try {
    // 1. 测试Gateway健康检查
    console.log('\n🧪 ========== 测试Gateway健康状态 ==========\n');
    await makeRequest('GET', `${GATEWAY_URL}/health`);
    console.log('✅ Gateway健康检查通过');
    
    // 2. 测试Auth服务健康检查
    console.log('\n🧪 ========== 测试Auth服务健康状态 ==========\n');
    await makeRequest('GET', `${AUTH_SERVICE_URL}/health`);
    console.log('✅ Auth服务健康检查通过');
    
    // 3. 测试Gateway代理注册（使用正确的双重前缀路径）
    console.log('\n🧪 ========== 测试Gateway代理注册 ==========');
    console.log('📍 代理路径: /api/auth/auth/register -> Auth服务 /auth/register\n');
    
    const testUser = {
      username: `quicktest_${Date.now()}`,
      email: `quicktest_${Date.now()}@example.com`,
      password: 'SecurePass2024!@#',
      confirmPassword: 'SecurePass2024!@#',
      acceptTerms: true,
      profile: {
        firstName: 'Quick',
        lastName: 'Test',
        language: 'zh-CN'
      }
    };
    
    await makeRequest('POST', `${GATEWAY_URL}/api/auth/auth/register`, testUser);
    console.log('✅ Gateway代理注册成功');
    
    console.log('\n🎉 快速测试完成！Gateway代理工作正常');
    
  } catch (error) {
    console.log('\n❌ 快速测试失败');
    console.log('错误详情:', error.message);
    process.exit(1);
  }
}

// 运行测试
runQuickTest().catch(console.error);
