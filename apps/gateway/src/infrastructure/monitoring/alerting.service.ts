import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

export interface AlertRule {
  id: string;
  name: string;
  condition: string;
  threshold: number;
  severity: 'low' | 'medium' | 'high' | 'critical';
  enabled: boolean;
  cooldown: number; // 冷却时间（毫秒）
}

export interface Alert {
  id: string;
  ruleId: string;
  message: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  timestamp: Date;
  resolved: boolean;
  resolvedAt?: Date;
  metadata?: Record<string, any>;
}

/**
 * 基础设施层 - 告警服务
 * 
 * 提供告警和通知功能：
 * - 告警规则管理
 * - 告警触发和恢复
 * - 告警通知发送
 * - 告警历史记录
 */
@Injectable()
export class AlertingService {
  private readonly logger = new Logger(AlertingService.name);
  private readonly alertRules = new Map<string, AlertRule>();
  private readonly activeAlerts = new Map<string, Alert>();
  private readonly alertHistory: Alert[] = [];
  private readonly lastAlertTime = new Map<string, number>();

  constructor(private readonly configService: ConfigService) {
    this.initializeDefaultRules();
  }

  /**
   * 初始化默认告警规则
   */
  private initializeDefaultRules() {
    const defaultRules: AlertRule[] = [
      {
        id: 'high_error_rate',
        name: '高错误率告警',
        condition: 'error_rate > threshold',
        threshold: 0.05, // 5%
        severity: 'high',
        enabled: true,
        cooldown: 300000, // 5分钟
      },
      {
        id: 'high_response_time',
        name: '高响应时间告警',
        condition: 'avg_response_time > threshold',
        threshold: 1000, // 1秒
        severity: 'medium',
        enabled: true,
        cooldown: 180000, // 3分钟
      },
      {
        id: 'low_memory',
        name: '内存不足告警',
        condition: 'memory_usage > threshold',
        threshold: 0.9, // 90%
        severity: 'critical',
        enabled: true,
        cooldown: 60000, // 1分钟
      },
      {
        id: 'high_cpu',
        name: 'CPU使用率过高告警',
        condition: 'cpu_usage > threshold',
        threshold: 0.8, // 80%
        severity: 'high',
        enabled: true,
        cooldown: 120000, // 2分钟
      },
    ];

    defaultRules.forEach(rule => {
      this.alertRules.set(rule.id, rule);
    });

    this.logger.log(`Initialized ${defaultRules.length} default alert rules`);
  }

  /**
   * 检查指标并触发告警
   */
  checkMetrics(metrics: Record<string, number>) {
    for (const [ruleId, rule] of this.alertRules) {
      if (!rule.enabled) continue;

      const shouldAlert = this.evaluateRule(rule, metrics);
      const isInCooldown = this.isInCooldown(ruleId);

      if (shouldAlert && !isInCooldown) {
        this.triggerAlert(rule, metrics);
      } else if (!shouldAlert && this.activeAlerts.has(ruleId)) {
        this.resolveAlert(ruleId);
      }
    }
  }

  /**
   * 评估告警规则
   */
  private evaluateRule(rule: AlertRule, metrics: Record<string, number>): boolean {
    try {
      switch (rule.id) {
        case 'high_error_rate':
          return (metrics.errorRate || 0) > rule.threshold;
        case 'high_response_time':
          return (metrics.averageResponseTime || 0) > rule.threshold;
        case 'low_memory':
          return (metrics.memoryUsage || 0) > rule.threshold;
        case 'high_cpu':
          return (metrics.cpuUsage || 0) > rule.threshold;
        default:
          return false;
      }
    } catch (error) {
      this.logger.error(`Error evaluating rule ${rule.id}:`, error);
      return false;
    }
  }

  /**
   * 检查是否在冷却期内
   */
  private isInCooldown(ruleId: string): boolean {
    const lastTime = this.lastAlertTime.get(ruleId);
    if (!lastTime) return false;

    const rule = this.alertRules.get(ruleId);
    if (!rule) return false;

    return Date.now() - lastTime < rule.cooldown;
  }

  /**
   * 触发告警
   */
  private triggerAlert(rule: AlertRule, metrics: Record<string, number>) {
    const alert: Alert = {
      id: `${rule.id}_${Date.now()}`,
      ruleId: rule.id,
      message: this.generateAlertMessage(rule, metrics),
      severity: rule.severity,
      timestamp: new Date(),
      resolved: false,
      metadata: { metrics, rule },
    };

    this.activeAlerts.set(rule.id, alert);
    this.alertHistory.push(alert);
    this.lastAlertTime.set(rule.id, Date.now());

    this.logger.warn(`Alert triggered: ${alert.message}`);
    this.sendNotification(alert);
  }

  /**
   * 解决告警
   */
  private resolveAlert(ruleId: string) {
    const alert = this.activeAlerts.get(ruleId);
    if (alert) {
      alert.resolved = true;
      alert.resolvedAt = new Date();
      this.activeAlerts.delete(ruleId);

      this.logger.log(`Alert resolved: ${alert.message}`);
      this.sendNotification(alert);
    }
  }

  /**
   * 生成告警消息
   */
  private generateAlertMessage(rule: AlertRule, metrics: Record<string, number>): string {
    switch (rule.id) {
      case 'high_error_rate':
        return `错误率过高: ${((metrics.errorRate || 0) * 100).toFixed(2)}% (阈值: ${(rule.threshold * 100).toFixed(2)}%)`;
      case 'high_response_time':
        return `响应时间过长: ${metrics.averageResponseTime || 0}ms (阈值: ${rule.threshold}ms)`;
      case 'low_memory':
        return `内存使用率过高: ${((metrics.memoryUsage || 0) * 100).toFixed(2)}% (阈值: ${(rule.threshold * 100).toFixed(2)}%)`;
      case 'high_cpu':
        return `CPU使用率过高: ${((metrics.cpuUsage || 0) * 100).toFixed(2)}% (阈值: ${(rule.threshold * 100).toFixed(2)}%)`;
      default:
        return `告警触发: ${rule.name}`;
    }
  }

  /**
   * 发送通知
   */
  private sendNotification(alert: Alert) {
    // 这里可以集成各种通知渠道
    // 例如：邮件、短信、Slack、钉钉等
    
    const notificationEnabled = this.configService.get('ALERTING_ENABLED', false);
    if (!notificationEnabled) return;

    // 模拟通知发送
    this.logger.log(`Notification sent for alert: ${alert.id}`);
    
    // TODO: 实现具体的通知渠道
    // - 邮件通知
    // - Webhook 通知
    // - 第三方集成（Slack、钉钉等）
  }

  /**
   * 获取活跃告警
   */
  getActiveAlerts(): Alert[] {
    return Array.from(this.activeAlerts.values());
  }

  /**
   * 获取告警历史
   */
  getAlertHistory(limit = 100): Alert[] {
    return this.alertHistory
      .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
      .slice(0, limit);
  }

  /**
   * 获取告警规则
   */
  getAlertRules(): AlertRule[] {
    return Array.from(this.alertRules.values());
  }

  /**
   * 更新告警规则
   */
  updateAlertRule(ruleId: string, updates: Partial<AlertRule>): boolean {
    const rule = this.alertRules.get(ruleId);
    if (!rule) return false;

    const updatedRule = { ...rule, ...updates };
    this.alertRules.set(ruleId, updatedRule);
    
    this.logger.log(`Alert rule updated: ${ruleId}`);
    return true;
  }

  /**
   * 手动解决告警
   */
  manualResolveAlert(ruleId: string): boolean {
    if (this.activeAlerts.has(ruleId)) {
      this.resolveAlert(ruleId);
      return true;
    }
    return false;
  }
}
