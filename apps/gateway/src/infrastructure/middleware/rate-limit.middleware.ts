import { Injectable, NestMiddleware, HttpException, HttpStatus } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { ConfigService } from '@nestjs/config';
import { RateLimitService, RateLimitConfig } from '../../core/rate-limit/rate-limit.service';
import { AuthenticatedRequest } from './auth.middleware';

export interface RateLimitOptions {
  global?: Partial<RateLimitConfig>;
  user?: Partial<RateLimitConfig>;
  ip?: Partial<RateLimitConfig>;
  api?: Partial<RateLimitConfig>;
  skipSuccessfulRequests?: boolean;
  skipFailedRequests?: boolean;
  keyGenerator?: (req: Request) => string;
  onLimitReached?: (req: Request, res: Response) => void;
}

@Injectable()
export class RateLimitMiddleware implements NestMiddleware {
  protected defaultOptions: RateLimitOptions;

  constructor(
    private readonly rateLimitService: RateLimitService,
    private readonly configService: ConfigService,
  ) {
    this.defaultOptions = {
      global: {
        windowMs: this.configService.get<number>('gateway.security.rateLimitWindowMs', 60000),
        max: this.configService.get<number>('gateway.security.rateLimitMax', 1000),
        strategy: 'sliding-window',
      },
      user: {
        windowMs: 60000, // 1分钟
        max: 100, // 每分钟100次
        strategy: 'sliding-window',
      },
      ip: {
        windowMs: 60000, // 1分钟
        max: 200, // 每分钟200次
        strategy: 'sliding-window',
      },
      api: {
        windowMs: 60000, // 1分钟
        max: 500, // 每分钟500次
        strategy: 'sliding-window',
      },
    };
  }

  async use(req: AuthenticatedRequest, res: Response, next: NextFunction) {
    try {
      const options = this.getOptionsForRequest(req);
      const rateLimitChecks = await this.performRateLimitChecks(req, options);
      
      // 检查是否有任何限流被触发
      const blockedCheck = rateLimitChecks.find(check => !check.result.allowed);
      
      if (blockedCheck) {
        this.setRateLimitHeaders(res, blockedCheck.result);
        
        if (options.onLimitReached) {
          options.onLimitReached(req, res);
        }
        
        throw new HttpException(
          {
            statusCode: HttpStatus.TOO_MANY_REQUESTS,
            message: `Rate limit exceeded for ${blockedCheck.type}`,
            error: 'Too Many Requests',
            retryAfter: blockedCheck.result.retryAfter,
            limit: blockedCheck.result.limit,
            remaining: blockedCheck.result.remaining,
            resetTime: blockedCheck.result.resetTime,
          },
          HttpStatus.TOO_MANY_REQUESTS,
        );
      }
      
      // 设置最严格的限流头部信息
      const mostRestrictive = this.getMostRestrictiveLimit(rateLimitChecks);
      this.setRateLimitHeaders(res, mostRestrictive.result);
      
      next();
    } catch (error) {
      if (error instanceof HttpException) {
        res.status(error.getStatus()).json(error.getResponse());
      } else {
        next(error);
      }
    }
  }

  private async performRateLimitChecks(req: AuthenticatedRequest, options: RateLimitOptions) {
    const checks = [];
    const endpoint = this.getEndpoint(req);
    const clientIp = this.getClientIp(req);

    // 1. 全局限流检查
    if (options.global) {
      const globalKey = this.rateLimitService.generateGlobalKey();
      const globalResult = await this.rateLimitService.checkRateLimit(globalKey, options.global);
      checks.push({ type: 'global', result: globalResult });
    }

    // 2. API 端点限流检查
    if (options.api) {
      const apiKey = this.rateLimitService.generateApiKey(endpoint);
      const apiResult = await this.rateLimitService.checkRateLimit(apiKey, options.api);
      checks.push({ type: 'api', result: apiResult });
    }

    // 3. IP 限流检查
    if (options.ip) {
      const ipKey = this.rateLimitService.generateIpKey(clientIp, endpoint);
      const ipResult = await this.rateLimitService.checkRateLimit(ipKey, options.ip);
      checks.push({ type: 'ip', result: ipResult });
    }

    // 4. 用户限流检查（如果已认证）
    if (options.user && req.auth?.authenticated && req.auth.user) {
      const userKey = this.rateLimitService.generateUserKey(req.auth.user.id, endpoint);
      const userResult = await this.rateLimitService.checkRateLimit(userKey, options.user);
      checks.push({ type: 'user', result: userResult });
    }

    return checks;
  }

  private getOptionsForRequest(req: AuthenticatedRequest): RateLimitOptions {
    // 根据请求特征动态调整限流配置
    const options = { ...this.defaultOptions };
    
    // 根据用户等级调整限流
    if (req.auth?.authenticated && req.auth.user) {
      const userLevel = req.auth.user.level || 1;
      const multiplier = Math.min(userLevel / 10 + 1, 5); // 最多5倍
      
      if (options.user) {
        options.user.max = Math.floor((options.user.max || 100) * multiplier);
      }
    }
    
    // 根据 API Key 权限调整限流
    if (req.auth?.apiKey?.rateLimit) {
      options.user = {
        windowMs: req.auth.apiKey.rateLimit.window,
        max: req.auth.apiKey.rateLimit.requests,
        strategy: 'sliding-window',
      };
    }
    
    // 根据端点类型调整限流
    const endpoint = this.getEndpoint(req);
    if (endpoint.includes('/auth/')) {
      // 认证相关端点更严格的限流
      options.ip = {
        ...options.ip,
        max: 20, // 每分钟20次
      };
    } else if (endpoint.includes('/upload/')) {
      // 上传端点更宽松的限流
      options.user = {
        ...options.user,
        max: 10, // 每分钟10次
      };
    }
    
    return options;
  }

  private setRateLimitHeaders(res: Response, result: any): void {
    res.setHeader('X-RateLimit-Limit', result.limit);
    res.setHeader('X-RateLimit-Remaining', result.remaining);
    res.setHeader('X-RateLimit-Reset', Math.ceil(result.resetTime.getTime() / 1000));
    
    if (result.retryAfter) {
      res.setHeader('Retry-After', result.retryAfter);
    }
  }

  private getMostRestrictiveLimit(checks: any[]) {
    return checks.reduce((most, current) => {
      if (current.result.remaining < most.result.remaining) {
        return current;
      }
      return most;
    });
  }

  private getEndpoint(req: Request): string {
    return `${req.method}:${req.route?.path || req.path}`;
  }

  private getClientIp(req: Request): string {
    return (
      req.get('X-Forwarded-For')?.split(',')[0]?.trim() ||
      req.get('X-Real-IP') ||
      req.get('X-Client-IP') ||
      req.connection.remoteAddress ||
      req.socket.remoteAddress ||
      '127.0.0.1'
    );
  }
}

/**
 * 严格限流中间件（用于敏感操作）
 */
@Injectable()
export class StrictRateLimitMiddleware extends RateLimitMiddleware {
  constructor(rateLimitService: RateLimitService, configService: ConfigService) {
    super(rateLimitService, configService);
    
    // 覆盖默认配置为更严格的限制
    this.defaultOptions = {
      global: {
        windowMs: 60000,
        max: 100,
        strategy: 'sliding-window',
      },
      user: {
        windowMs: 60000,
        max: 10, // 每分钟10次
        strategy: 'sliding-window',
      },
      ip: {
        windowMs: 60000,
        max: 20, // 每分钟20次
        strategy: 'sliding-window',
      },
      api: {
        windowMs: 60000,
        max: 50, // 每分钟50次
        strategy: 'sliding-window',
      },
    };
  }
}

/**
 * 宽松限流中间件（用于公开 API）
 */
@Injectable()
export class LenientRateLimitMiddleware extends RateLimitMiddleware {
  constructor(rateLimitService: RateLimitService, configService: ConfigService) {
    super(rateLimitService, configService);
    
    // 覆盖默认配置为更宽松的限制
    this.defaultOptions = {
      global: {
        windowMs: 60000,
        max: 5000,
        strategy: 'sliding-window',
      },
      user: {
        windowMs: 60000,
        max: 1000, // 每分钟1000次
        strategy: 'sliding-window',
      },
      ip: {
        windowMs: 60000,
        max: 500, // 每分钟500次
        strategy: 'sliding-window',
      },
      api: {
        windowMs: 60000,
        max: 2000, // 每分钟2000次
        strategy: 'sliding-window',
      },
    };
  }
}
