import { Injectable, NestMiddleware, Logger } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { LoggingService } from '../../infrastructure/logging/logging.service';
import { TracingService } from '../../infrastructure/tracing/tracing.service';

export interface RequestWithTiming extends Request {
  startTime?: number;
  requestId?: string;
}

@Injectable()
export class LoggingMiddleware implements NestMiddleware {
  private readonly logger = new Logger(LoggingMiddleware.name);

  constructor(
    private readonly loggingService: LoggingService,
    private readonly tracingService: TracingService,
  ) {}

  use(req: RequestWithTiming, res: Response, next: NextFunction) {
    // 记录请求开始时间
    req.startTime = Date.now();
    req.requestId = this.generateRequestId();

    // 开始追踪
    const span = this.tracingService.traceHttpRequest(req, res);

    // 设置请求 ID 头
    res.setHeader('X-Request-ID', req.requestId);

    // 监听响应完成事件
    res.on('finish', () => {
      const responseTime = Date.now() - req.startTime!;
      
      // 记录访问日志
      this.loggingService.logAccess(req, res, responseTime);
      
      // 完成追踪
      const status = res.statusCode >= 400 ? 'error' : 'ok';
      this.tracingService.setSpanTag(span, 'http.status_code', res.statusCode);
      this.tracingService.finishSpan(span, status);
      
      // 记录性能日志
      if (responseTime > 1000) { // 超过1秒的请求
        this.loggingService.logPerformance('slow_request', responseTime, {
          method: req.method,
          url: req.originalUrl || req.url,
          statusCode: res.statusCode,
          userAgent: req.get('user-agent'),
        });
      }
    });

    // 监听错误事件
    res.on('error', (error) => {
      this.loggingService.logError(error, 'HTTP Response', {
        requestId: req.requestId,
        method: req.method,
        url: req.originalUrl || req.url,
      });
      
      this.tracingService.logToSpan(span, 'error', error.message, {
        'error.kind': error.name,
        'error.object': error,
      });
      this.tracingService.finishSpan(span, 'error');
    });

    next();
  }

  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}
