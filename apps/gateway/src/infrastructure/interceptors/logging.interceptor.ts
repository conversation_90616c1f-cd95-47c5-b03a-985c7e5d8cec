import {
  Injectable,
  NestInterceptor,
  ExecutionContext,
  CallHandler,
  Logger,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { tap, catchError } from 'rxjs/operators';
import { Request, Response } from 'express';

/**
 * 日志拦截器
 * 
 * 记录所有 HTTP 请求和响应的详细信息
 * 包括请求时间、响应时间、状态码、用户信息等
 */
@Injectable()
export class LoggingInterceptor implements NestInterceptor {
  private readonly logger = new Logger(LoggingInterceptor.name);

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const startTime = Date.now();
    const ctx = context.switchToHttp();
    const request = ctx.getRequest<Request>();
    const response = ctx.getResponse<Response>();

    // 生成请求 ID
    const requestId = this.generateRequestId();
    request.headers['x-request-id'] = requestId;
    response.setHeader('X-Request-ID', requestId);

    // 记录请求开始
    this.logRequest(request, requestId);

    return next.handle().pipe(
      tap((data) => {
        // 记录成功响应
        const duration = Date.now() - startTime;
        this.logResponse(request, response, duration, requestId, data);
      }),
      catchError((error) => {
        // 记录错误响应
        const duration = Date.now() - startTime;
        this.logError(request, response, duration, requestId, error);
        throw error;
      }),
    );
  }

  /**
   * 记录请求信息
   */
  private logRequest(request: Request, requestId: string): void {
    const { method, url, headers, body, query, params } = request;
    const userAgent = headers['user-agent'];
    const ip = this.getClientIp(request);
    const userId = (request as any).user?.id;

    const logData = {
      type: 'REQUEST',
      requestId,
      method,
      url,
      ip,
      userAgent,
      userId,
      query: Object.keys(query).length > 0 ? query : undefined,
      params: Object.keys(params).length > 0 ? params : undefined,
      bodySize: this.getBodySize(body),
      timestamp: new Date().toISOString(),
    };

    this.logger.log(`→ ${method} ${url}`, JSON.stringify(logData));
  }

  /**
   * 记录成功响应信息
   */
  private logResponse(
    request: Request,
    response: Response,
    duration: number,
    requestId: string,
    data: any,
  ): void {
    const { method, url } = request;
    const { statusCode } = response;
    const userId = (request as any).user?.id;

    const logData = {
      type: 'RESPONSE',
      requestId,
      method,
      url,
      statusCode,
      duration,
      userId,
      responseSize: this.getResponseSize(data),
      timestamp: new Date().toISOString(),
    };

    const logLevel = statusCode >= 400 ? 'warn' : 'log';
    this.logger[logLevel](`← ${method} ${url} ${statusCode} ${duration}ms`, JSON.stringify(logData));
  }

  /**
   * 记录错误响应信息
   */
  private logError(
    request: Request,
    response: Response,
    duration: number,
    requestId: string,
    error: any,
  ): void {
    const { method, url } = request;
    const statusCode = response.statusCode || 500;
    const userId = (request as any).user?.id;

    const logData = {
      type: 'ERROR',
      requestId,
      method,
      url,
      statusCode,
      duration,
      userId,
      error: {
        name: error.name,
        message: error.message,
        stack: process.env.NODE_ENV === 'development' ? error.stack : undefined,
      },
      timestamp: new Date().toISOString(),
    };

    this.logger.error(`✗ ${method} ${url} ${statusCode} ${duration}ms`, JSON.stringify(logData));
  }

  /**
   * 获取客户端 IP 地址
   */
  private getClientIp(request: Request): string {
    const forwarded = request.headers['x-forwarded-for'];
    if (forwarded) {
      const forwardedStr = Array.isArray(forwarded) ? forwarded[0] : forwarded;
      return forwardedStr.split(',')[0].trim();
    }

    const realIp = request.headers['x-real-ip'];
    if (realIp && typeof realIp === 'string') {
      return realIp;
    }

    return request.connection?.remoteAddress ||
           request.socket?.remoteAddress ||
           'unknown';
  }

  /**
   * 生成请求 ID
   */
  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 获取请求体大小
   */
  private getBodySize(body: any): number {
    if (!body) return 0;
    
    try {
      return JSON.stringify(body).length;
    } catch {
      return 0;
    }
  }

  /**
   * 获取响应体大小
   */
  private getResponseSize(data: any): number {
    if (!data) return 0;
    
    try {
      return JSON.stringify(data).length;
    } catch {
      return 0;
    }
  }
}
