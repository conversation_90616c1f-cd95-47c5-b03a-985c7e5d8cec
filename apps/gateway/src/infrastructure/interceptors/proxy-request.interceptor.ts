import {
  Injectable,
  NestInterceptor,
  ExecutionContext,
  CallHandler,
  Logger,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { tap, catchError } from 'rxjs/operators';
import { Request, Response } from 'express';
import { MetricsService } from '../monitoring/metrics.service';
import { ROUTE_CONSTANTS } from '../../common/constants';

/**
 * 代理请求拦截器
 * 
 * 处理代理请求的前置和后置逻辑：
 * - 请求开始时记录日志和指标
 * - 请求完成时记录响应时间和状态
 * - 错误处理和监控
 */
@Injectable()
export class ProxyRequestInterceptor implements NestInterceptor {
  private readonly logger = new Logger(ProxyRequestInterceptor.name);

  constructor(private readonly metricsService: MetricsService) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest<Request>();
    const response = context.switchToHttp().getResponse<Response>();
    
    const startTime = Date.now();
    const requestId = (request as any).requestId || this.generateRequestId();
    const fullPath = request.originalUrl.split('?')[0]; // 使用完整路径，去掉查询参数

    // 前置处理：记录请求开始
    this.logger.debug(`[${requestId}] Proxy request started: ${request.method} ${fullPath}`);
    
    // 记录请求指标
    this.metricsService.recordProxyRequest(request.method, fullPath);

    return next.handle().pipe(
      tap(() => {
        // 后置处理：记录成功响应
        const duration = Date.now() - startTime;
        this.logger.debug(`[${requestId}] Proxy request completed: ${request.method} ${fullPath} - ${duration}ms`);
        
        // 记录成功指标
        this.metricsService.recordProxyResponse(
          request.method,
          fullPath,
          response.statusCode,
          duration
        );
      }),
      catchError((error) => {
        // 错误处理：记录失败响应
        const duration = Date.now() - startTime;
        this.logger.error(`[${requestId}] Proxy request failed: ${request.method} ${fullPath} - ${duration}ms`, error.stack);
        
        // 记录错误指标
        this.metricsService.recordProxyError(
          request.method,
          fullPath,
          error.status || 500,
          duration
        );

        // 重新抛出错误
        throw error;
      })
    );
  }

  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}
