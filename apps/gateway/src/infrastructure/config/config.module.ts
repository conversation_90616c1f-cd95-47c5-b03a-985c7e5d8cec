import { Module } from '@nestjs/common';

// 导入配置管理服务
import { ConfigManagerService } from '../../infrastructure/config/config-manager.service';

/**
 * 配置管理模块
 * 
 * 提供网关配置管理功能，包括：
 * - 动态配置加载和更新
 * - 配置验证和校验
 * - 配置热重载
 * - 配置版本管理
 * 
 * 职责范围：
 * - 管理网关的各种配置参数
 * - 支持配置的动态更新和热重载
 * - 提供配置的验证和校验机制
 * - 实现配置的版本控制和回滚
 */
@Module({
  providers: [
    ConfigManagerService,
  ],
  exports: [
    ConfigManagerService,
  ],
})
export class ConfigManagerModule {}
