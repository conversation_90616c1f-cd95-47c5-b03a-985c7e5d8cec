import {
  Injectable,
  CanActivate,
  ExecutionContext,
  UnauthorizedException,
  Logger,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { Request } from 'express';

import { UserService } from '../user.service';

/**
 * 认证守卫
 * 
 * 验证 HTTP 请求的 JWT 令牌，确保只有经过认证的用户
 * 才能访问受保护的端点
 */
@Injectable()
export class AuthGuard implements CanActivate {
  private readonly logger = new Logger(AuthGuard.name);

  constructor(
    private readonly userService: UserService,
    private readonly reflector: Reflector,
  ) {}

  /**
   * 验证请求的认证状态
   */
  async canActivate(context: ExecutionContext): Promise<boolean> {
    // 检查是否标记为公开端点
    const isPublic = this.reflector.getAllAndOverride<boolean>('isPublic', [
      context.getHandler(),
      context.getClass(),
    ]);

    if (isPublic) {
      return true;
    }

    const request = context.switchToHttp().getRequest<Request>();
    const token = this.extractTokenFromHeader(request);

    if (!token) {
      this.logger.warn(`Authentication failed: No token provided for ${request.url}`);
      throw new UnauthorizedException('Authentication required');
    }

    try {
      // 验证令牌
      const payload = await this.userService.verifyToken(token);
      
      // 将用户信息和令牌附加到请求对象
      (request as any).user = {
        id: payload.sub,
        username: payload.username,
        email: payload.email,
        roles: payload.roles || [],
        permissions: payload.permissions || [],
      };
      (request as any).token = token;

      this.logger.debug(`Authentication successful for user: ${payload.sub}`);
      return true;

    } catch (error) {
      this.logger.error(`Authentication failed: ${error.message}`);
      
      if (error.name === 'JsonWebTokenError') {
        throw new UnauthorizedException('Invalid token');
      } else if (error.name === 'TokenExpiredError') {
        throw new UnauthorizedException('Token expired');
      } else if (error instanceof UnauthorizedException) {
        throw error;
      } else {
        throw new UnauthorizedException('Authentication failed');
      }
    }
  }

  /**
   * 从请求头中提取 JWT 令牌
   */
  private extractTokenFromHeader(request: Request): string | undefined {
    const authHeader = request.headers.authorization;
    
    if (!authHeader) {
      return undefined;
    }

    const [type, token] = authHeader.split(' ');
    
    if (type !== 'Bearer' || !token) {
      return undefined;
    }

    return token;
  }
}
