import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsOptional, <PERSON><PERSON><PERSON><PERSON>, Min, Max, Length } from 'class-validator';

/**
 * 角色信息DTO
 */
export class CharacterInfoDto {
  @ApiProperty({ description: '角色ID' })
  characterId: string;

  @ApiProperty({ description: '用户ID' })
  userId: string;

  @ApiProperty({ description: '区服ID' })
  serverId: string;

  @ApiProperty({ description: '角色名称' })
  name: string;

  @ApiProperty({ description: '角色等级' })
  level: number;

  @ApiProperty({ description: '头像图标ID' })
  faceIcon: number;

  @ApiProperty({ description: '角色创建时间' })
  createdAt: Date;

  @ApiProperty({ description: '最后活跃时间' })
  lastActiveAt: Date;

  @ApiPropertyOptional({ description: '角色状态', enum: ['active', 'inactive', 'banned'] })
  status?: string;

  @ApiPropertyOptional({ description: '是否为新手角色' })
  isNewbie?: boolean;
}

/**
 * 创建角色请求DTO
 */
export class CreateCharacterRequestDto {
  @ApiProperty({ description: '区服ID' })
  @IsString()
  @IsNotEmpty()
  serverId: string;

  @ApiProperty({ description: '角色名称', minLength: 2, maxLength: 12 })
  @IsString()
  @IsNotEmpty()
  @Length(2, 12, { message: '角色名称长度必须在2-12个字符之间' })
  name: string;

  @ApiProperty({ description: '头像图标ID', minimum: 1, maximum: 100 })
  @IsNumber()
  @Min(1, { message: '头像图标ID必须大于0' })
  @Max(100, { message: '头像图标ID不能超过100' })
  faceIcon: number;

  @ApiPropertyOptional({ description: '角色资质', minimum: 1, maximum: 5 })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(5)
  qualified?: number;
}

/**
 * 角色登录请求DTO
 */
export class CharacterLoginRequestDto {
  @ApiProperty({ description: '区服ID' })
  @IsString()
  @IsNotEmpty()
  serverId: string;

  @ApiProperty({ description: '角色ID' })
  @IsString()
  @IsNotEmpty()
  characterId: string;
}

/**
 * 获取角色列表请求DTO
 */
export class GetCharacterListRequestDto {
  @ApiProperty({ description: '区服ID' })
  @IsString()
  @IsNotEmpty()
  serverId: string;
}

/**
 * 角色登录响应DTO
 */
export class CharacterLoginResponseDto {
  @ApiProperty({ description: '是否成功' })
  success: boolean;

  @ApiProperty({ description: '响应消息' })
  message: string;

  @ApiProperty({ description: '角色Token' })
  characterToken: string;

  @ApiProperty({ description: 'Token过期时间（秒）' })
  expiresIn: number;

  @ApiProperty({ description: 'Token过期时间戳' })
  expiresAt: Date;

  @ApiProperty({ description: '角色信息', type: CharacterInfoDto })
  character: CharacterInfoDto;

  @ApiProperty({ description: '区服信息' })
  server: {
    id: string;
    name: string;
    status: string;
  };

  @ApiProperty({ description: '会话信息' })
  session: {
    id: string;
    expiresAt: Date;
  };
}

/**
 * 角色列表响应DTO
 */
export class CharacterListResponseDto {
  @ApiProperty({ description: '是否成功' })
  success: boolean;

  @ApiProperty({ description: '响应消息' })
  message: string;

  @ApiProperty({ description: '角色列表', type: [CharacterInfoDto] })
  characters: CharacterInfoDto[];

  @ApiProperty({ description: '角色总数' })
  totalCharacters: number;

  @ApiProperty({ description: '区服信息' })
  server: {
    id: string;
    name: string;
    status: string;
  };
}

/**
 * 创建角色响应DTO
 */
export class CreateCharacterResponseDto {
  @ApiProperty({ description: '是否成功' })
  success: boolean;

  @ApiProperty({ description: '响应消息' })
  message: string;

  @ApiProperty({ description: '创建的角色信息', type: CharacterInfoDto })
  character: CharacterInfoDto;

  @ApiProperty({ description: '区服信息' })
  server: {
    id: string;
    name: string;
    status: string;
  };
}

/**
 * 角色Token验证请求DTO
 */
export class ValidateCharacterTokenRequestDto {
  @ApiProperty({
    description: '角色Token',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...'
  })
  @IsString()
  @IsNotEmpty()
  characterToken: string;
}

/**
 * 角色Token载荷DTO
 */
export class CharacterTokenPayloadDto {
  @ApiProperty({ description: '用户ID' })
  userId: string;

  @ApiProperty({ description: '角色ID' })
  characterId: string;

  @ApiProperty({ description: '区服ID' })
  serverId: string;

  @ApiProperty({ description: '角色名称' })
  characterName: string;

  @ApiProperty({ description: 'Token作用域', example: 'character' })
  scope: string;

  @ApiProperty({ description: 'Token类型', example: 'character' })
  type: string;

  @ApiProperty({ description: '会话ID' })
  sessionId: string;

  @ApiProperty({ description: '设备ID' })
  deviceId: string;

  @ApiProperty({ description: '签发时间（Unix时间戳）' })
  iat: number;

  @ApiProperty({ description: '过期时间（Unix时间戳）' })
  exp: number;

  @ApiProperty({ description: 'Token唯一标识' })
  jti: string;
}

/**
 * 角色Token验证响应DTO
 */
export class ValidateCharacterTokenResponseDto {
  @ApiProperty({ description: 'Token是否有效' })
  valid: boolean;

  @ApiProperty({ description: '验证消息' })
  message: string;

  @ApiPropertyOptional({
    description: 'Token载荷信息（验证成功时返回）',
    type: CharacterTokenPayloadDto
  })
  payload?: CharacterTokenPayloadDto;

  @ApiPropertyOptional({
    description: '角色信息（可选）',
    type: CharacterInfoDto
  })
  character?: CharacterInfoDto;

  @ApiPropertyOptional({ description: '区服信息（可选）' })
  server?: {
    id: string;
    name: string;
    status: string;
  };
}

/**
 * 通用响应DTO基类
 */
export class BaseResponseDto<T = any> {
  @ApiProperty({ description: '是否成功' })
  success: boolean;

  @ApiProperty({ description: '响应消息' })
  message: string;

  @ApiProperty({ description: '响应数据' })
  data?: T;

  @ApiPropertyOptional({ description: '错误代码' })
  errorCode?: string;

  @ApiPropertyOptional({ description: '时间戳' })
  timestamp?: Date;
}
