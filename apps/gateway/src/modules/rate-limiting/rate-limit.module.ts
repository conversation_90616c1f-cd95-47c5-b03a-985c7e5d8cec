import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { RedisModule } from '@common/redis';

// 限流服务和守卫
import { RateLimitService } from './services/rate-limit.service';
import { RateLimitGuard } from './guards/rate-limit.guard';

/**
 * 限流功能模块
 *
 * 职责：
 * - 提供多维度限流控制
 * - 实现多种限流算法
 * - 防止系统过载和恶意攻击
 *
 * 依赖分析：
 * - ConfigModule：获取限流配置参数
 * - RedisModule：分布式限流计数器存储
 */
@Module({
  imports: [
    ConfigModule,
    RedisModule,
  ],
  providers: [
    RateLimitService,
    RateLimitGuard,
  ],
  exports: [
    RateLimitService,
    RateLimitGuard,
  ],
})
export class RateLimitingModule {}
