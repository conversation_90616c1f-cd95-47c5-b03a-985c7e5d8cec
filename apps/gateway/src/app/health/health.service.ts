import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { RedisService } from '@common/redis';
import { MICROSERVICE_NAMES } from '@shared/constants';
import { LoadBalancerService } from '../../core/load-balancer/load-balancer.service';
import * as os from 'os';

/**
 * 健康检查服务
 * 
 * 提供系统健康状态监控和信息收集
 */
@Injectable()
export class HealthService {
  private readonly logger = new Logger(HealthService.name);

  constructor(
    private readonly configService: ConfigService,
    private readonly redisService: RedisService,
    private readonly loadBalancerService: LoadBalancerService,
  ) {}

  /**
   * 获取系统信息
   */
  async getSystemInfo() {
    const memoryUsage = process.memoryUsage();
    const cpuUsage = process.cpuUsage();
    
    return {
      service: 'gateway',
      version: '1.0.0',
      environment: this.configService.get('NODE_ENV', 'development'),
      uptime: process.uptime(),
      timestamp: new Date().toISOString(),
      system: {
        platform: os.platform(),
        arch: os.arch(),
        nodeVersion: process.version,
        totalMemory: os.totalmem(),
        freeMemory: os.freemem(),
        cpuCount: os.cpus().length,
        loadAverage: os.loadavg(),
      },
      process: {
        pid: process.pid,
        memory: {
          rss: memoryUsage.rss,
          heapTotal: memoryUsage.heapTotal,
          heapUsed: memoryUsage.heapUsed,
          external: memoryUsage.external,
          arrayBuffers: memoryUsage.arrayBuffers,
        },
        cpu: {
          user: cpuUsage.user,
          system: cpuUsage.system,
        },
      },
    };
  }

  /**
   * 获取服务状态
   */
  async getServiceStatus() {
    const redisStatus = await this.checkRedisStatus();
    const microservicesStatus = await this.checkMicroservicesStatus();

    return {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      services: {
        redis: redisStatus,
        database: { status: 'up' }, // 这里可以添加数据库检查
        ...microservicesStatus,
      },
    };
  }

  /**
   * 检查微服务状态 - 使用负载均衡器的真实健康检查
   * 公开方法，供微服务健康检查控制器使用
   */
  async checkMicroservicesStatus() {
    const services = {};

    // 使用共享常量中的微服务列表（排除网关自身）
    const MICROSERVICES = Object.values(MICROSERVICE_NAMES).filter(
      serviceName => serviceName !== MICROSERVICE_NAMES.GATEWAY_SERVICE
    );

    // 并行检查所有微服务的健康状态
    const healthChecks = MICROSERVICES.map(async (serviceName) => {
      try {
        // 使用负载均衡器检查服务实例健康状态
        const instance = await this.loadBalancerService.getServiceInstance(serviceName);
        if (!instance) {
          return { serviceName, status: 'down', reason: 'No instance available' };
        }

        // 尝试HTTP健康检查
        const axios = require('axios');
        const response = await axios.get(`${instance.url}/health`, {
          timeout: 3000,
          validateStatus: (status) => status < 500,
        });

        return {
          serviceName,
          status: response.status === 200 ? 'up' : 'down',
          responseTime: response.headers['x-response-time'] || 'unknown'
        };
      } catch (error) {
        return {
          serviceName,
          status: 'down',
          reason: error.code === 'ECONNREFUSED' ? 'Service not running' : error.message
        };
      }
    });

    const results = await Promise.all(healthChecks);

    // 转换为对象格式
    results.forEach(result => {
      services[result.serviceName] = {
        status: result.status,
        ...(result.reason && { reason: result.reason }),
        ...(result.responseTime && { responseTime: result.responseTime }),
      };
    });

    return services;
  }

  /**
   * 检查 Redis 状态
   */
  private async checkRedisStatus() {
    try {
      const isHealthy = await this.redisService.ping();
      const info = await this.redisService.getInfo();
      
      return {
        status: isHealthy ? 'up' : 'down',
        responseTime: Date.now(), // 这里可以测量实际响应时间
        info: {
          version: info.redis_version,
          mode: info.redis_mode,
          connectedClients: info.connected_clients,
          usedMemory: info.used_memory,
          usedMemoryHuman: info.used_memory_human,
        },
      };
    } catch (error) {
      this.logger.error(`Redis health check failed: ${error.message}`);
      return {
        status: 'down',
        error: error.message,
      };
    }
  }
}
