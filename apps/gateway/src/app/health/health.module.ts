import {Module} from '@nestjs/common';
import {TerminusModule} from '@nestjs/terminus';
import {HttpModule} from '@nestjs/axios';
import {ConfigModule} from '@nestjs/config';

import {HealthService} from './health.service';
import {RedisHealthIndicator} from './indicators/redis-health.indicator';
import {MicroserviceHealthIndicator} from './indicators/microservice-health.indicator';
import { CoreModule } from '../../core/shared/core.module';

// 导入优化的控制器
import { StandardHealthController } from './controllers/standard-health.controller';
import { MicroserviceHealthController } from './controllers/microservice-health.controller';

/**
 * 健康检查模块（优化版）
 *
 * 基于微服务架构最佳实践设计：
 * - 标准健康检查（符合Kubernetes探针标准）
 * - 微服务健康检查（支持单个和批量检查）
 * - 监控指标分离（移至监控模块）
 * - 清晰的职责分离和架构设计
 */
@Module({
  imports: [
    ConfigModule,
    TerminusModule,
    HttpModule,
    // 导入CoreModule以获取LoadBalancerService
    CoreModule,
  ],
  controllers: [
    // 优化的健康检查控制器
    StandardHealthController,
    MicroserviceHealthController,
  ],
  providers: [
    HealthService,
    RedisHealthIndicator,
    MicroserviceHealthIndicator,
  ],
  exports: [
    HealthService,
    RedisHealthIndicator,
    MicroserviceHealthIndicator,
  ],
})
export class HealthModule {}
