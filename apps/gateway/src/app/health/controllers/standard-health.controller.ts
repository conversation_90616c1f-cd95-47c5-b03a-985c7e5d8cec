import { Controller, Get } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { HealthCheckService, MemoryHealthIndicator } from '@nestjs/terminus';
import { RedisHealthIndicator } from '../indicators/redis-health.indicator';

/**
 * 标准健康检查控制器
 * 符合Kubernetes探针和微服务架构最佳实践
 */
@ApiTags('标准健康检查')
@Controller('health')
export class StandardHealthController {
  constructor(
    private readonly healthCheckService: HealthCheckService,
    private readonly memoryHealthIndicator: MemoryHealthIndicator,
    private readonly redisHealthIndicator: RedisHealthIndicator,
  ) {}

  /**
   * 存活检查 - Kubernetes Liveness Probe
   * 用途：检查服务是否存活，失败时重启容器
   * 检查内容：最基础的服务可用性
   */
  @Get('live')
  @ApiOperation({ 
    summary: '存活检查', 
    description: 'Kubernetes Liveness Probe - 检查服务是否存活' 
  })
  @ApiResponse({ 
    status: 200, 
    description: '服务存活',
    schema: {
      example: {
        status: 'ok',
        timestamp: '2025-07-17T02:19:06.817Z',
        service: 'gateway',
        version: '1.0.0'
      }
    }
  })
  @ApiResponse({ 
    status: 503, 
    description: '服务不可用' 
  })
  async checkLiveness() {
    try {
      // 最基础的存活检查 - 只检查核心功能
      const result = await this.healthCheckService.check([
        // 检查内存使用是否在合理范围内
        () => this.memoryHealthIndicator.checkHeap('memory_heap', 500 * 1024 * 1024),
      ]);

      return {
        status: 'ok',
        timestamp: new Date().toISOString(),
        service: 'gateway',
        version: '1.0.0',
        info: result.info,
        details: result.details,
      };
    } catch (error) {
      throw error; // NestJS会自动返回503状态码
    }
  }

  /**
   * 就绪检查 - Kubernetes Readiness Probe
   * 用途：检查服务是否准备好接收流量
   * 检查内容：关键依赖服务状态
   */
  @Get('ready')
  @ApiOperation({ 
    summary: '就绪检查', 
    description: 'Kubernetes Readiness Probe - 检查服务是否准备好接收流量' 
  })
  @ApiResponse({ 
    status: 200, 
    description: '服务就绪',
    schema: {
      example: {
        status: 'ok',
        timestamp: '2025-07-17T02:19:06.817Z',
        checks: {
          redis: { status: 'up', responseTime: '5ms' },
          memory: { status: 'up', usage: '45%' }
        }
      }
    }
  })
  @ApiResponse({ 
    status: 503, 
    description: '服务未就绪' 
  })
  async checkReadiness() {
    try {
      // 就绪检查 - 检查关键依赖服务
      const result = await this.healthCheckService.check([
        // 检查Redis连接
        () => this.redisHealthIndicator.isHealthy('redis'),
        // 检查内存使用
        () => this.memoryHealthIndicator.checkHeap('memory_heap', 300 * 1024 * 1024),
      ]);

      return {
        status: 'ok',
        timestamp: new Date().toISOString(),
        checks: this.formatHealthChecks(result.details),
        info: result.info,
        details: result.details,
      };
    } catch (error) {
      throw error; // NestJS会自动返回503状态码
    }
  }

  /**
   * 启动检查 - Kubernetes Startup Probe
   * 用途：检查服务是否完成启动过程
   * 检查内容：启动状态和进度
   */
  @Get('startup')
  @ApiOperation({ 
    summary: '启动检查', 
    description: 'Kubernetes Startup Probe - 检查服务启动完成状态' 
  })
  @ApiResponse({ 
    status: 200, 
    description: '服务启动完成',
    schema: {
      example: {
        status: 'ok',
        timestamp: '2025-07-17T02:19:06.817Z',
        startup: {
          phase: 'completed',
          duration: '15.2s',
          steps: {
            database_connection: 'completed',
            redis_connection: 'completed',
            microservice_discovery: 'completed'
          }
        }
      }
    }
  })
  @ApiResponse({ 
    status: 503, 
    description: '服务启动未完成' 
  })
  async checkStartup() {
    try {
      // 启动检查 - 检查启动过程完成情况
      const result = await this.healthCheckService.check([
        () => this.redisHealthIndicator.isHealthy('redis'),
        () => this.memoryHealthIndicator.checkHeap('memory_heap', 300 * 1024 * 1024),
      ]);

      // 计算启动时间（从进程启动到现在）
      const uptimeSeconds = process.uptime();
      const startupDuration = `${uptimeSeconds.toFixed(1)}s`;

      return {
        status: 'ok',
        timestamp: new Date().toISOString(),
        startup: {
          phase: 'completed',
          duration: startupDuration,
          steps: {
            process_initialization: 'completed',
            redis_connection: result.details?.redis?.status === 'up' ? 'completed' : 'failed',
            memory_allocation: result.details?.memory_heap?.status === 'up' ? 'completed' : 'failed',
            microservice_discovery: 'completed', // 可以根据实际情况调整
          }
        },
        info: result.info,
        details: result.details,
      };
    } catch (error) {
      throw error; // NestJS会自动返回503状态码
    }
  }

  /**
   * 格式化健康检查结果
   * 将NestJS健康检查结果转换为标准格式
   */
  private formatHealthChecks(details: any): Record<string, any> {
    const formatted: Record<string, any> = {};

    if (details) {
      Object.keys(details).forEach(key => {
        const check = details[key];
        formatted[key] = {
          status: check.status || 'unknown',
          ...(check.responseTime && { responseTime: check.responseTime }),
          ...(check.error && { error: check.error }),
        };
      });
    }

    return formatted;
  }
}
