import { Controller, Get, Post, Body, Param, Query, Header } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';

// 导入基础设施层监控服务
import { MetricsService } from '../../infrastructure/monitoring/metrics.service';
import { AlertingService } from '../../infrastructure/monitoring/alerting.service';
import { TracingService } from '../../infrastructure/monitoring/tracing.service';
import { ConfigManagerService } from '../../infrastructure/config/config-manager.service';

/**
 * 监控指标控制器（优化版）
 *
 * 基于微服务架构最佳实践重构：
 * - 符合Prometheus标准的指标格式
 * - 分离健康检查和监控指标
 * - 提供标准化的监控数据接口
 * - 支持Kubernetes和Docker监控集成
 */
@ApiTags('监控指标')
@Controller()
export class MonitoringController {
  constructor(
    private readonly metricsService: MetricsService,
    private readonly alertingService: AlertingService,
    private readonly tracingService: TracingService,
    private readonly configManagerService: ConfigManagerService,
  ) {}

  /**
   * Prometheus指标端点（标准化）
   * 用途：标准Prometheus监控指标
   * 格式：Prometheus文本格式
   */
  @Get('metrics')
  @Header('Content-Type', 'text/plain; version=0.0.4; charset=utf-8')
  @ApiOperation({
    summary: 'Prometheus指标',
    description: '提供标准Prometheus格式的监控指标'
  })
  @ApiResponse({
    status: 200,
    description: 'Prometheus格式指标',
    content: {
      'text/plain': {
        example: `# HELP gateway_requests_total Total number of requests
# TYPE gateway_requests_total counter
gateway_requests_total{method="GET",status="200"} 1234`
      }
    }
  })
  async getPrometheusMetrics(): Promise<string> {
    try {
      // 优先使用现有的metrics服务
      const existingMetrics = await this.metricsService.getMetrics();
      if (existingMetrics && typeof existingMetrics === 'string') {
        return existingMetrics;
      }

      // 如果现有服务返回的不是标准格式，则构建标准格式
      const stats = await this.getGatewayStatsInternal();
      const timestamp = Date.now();

      const metrics = [
        '# HELP gateway_requests_total Total number of requests processed by the gateway',
        '# TYPE gateway_requests_total counter',
        `gateway_requests_total ${stats.totalRequests || 0} ${timestamp}`,
        '',
        '# HELP gateway_active_connections Current number of active connections',
        '# TYPE gateway_active_connections gauge',
        `gateway_active_connections ${stats.activeConnections || 0} ${timestamp}`,
        '',
        '# HELP gateway_uptime_seconds Gateway uptime in seconds',
        '# TYPE gateway_uptime_seconds counter',
        `gateway_uptime_seconds ${process.uptime()} ${timestamp}`,
        '',
      ];

      return metrics.join('\n');
    } catch (error) {
      // 即使出错也要返回基础指标
      return `# HELP gateway_up Gateway availability
# TYPE gateway_up gauge
gateway_up 0 ${Date.now()}`;
    }
  }

  /**
   * 获取活跃告警
   */
  @Get('alerts/active')
  @ApiOperation({ summary: '获取活跃告警' })
  @ApiResponse({ status: 200, description: '当前活跃的告警列表' })
  async getActiveAlerts() {
    return this.alertingService.getActiveAlerts();
  }

  /**
   * 获取告警历史
   */
  @Get('alerts/history')
  @ApiOperation({ summary: '获取告警历史' })
  @ApiResponse({ status: 200, description: '告警历史记录' })
  async getAlertHistory(@Query('limit') limit?: string) {
    const limitNum = limit ? parseInt(limit, 10) : 100;
    return this.alertingService.getAlertHistory(limitNum);
  }

  /**
   * 获取告警规则
   */
  @Get('alerts/rules')
  @ApiOperation({ summary: '获取告警规则' })
  @ApiResponse({ status: 200, description: '告警规则配置' })
  async getAlertRules() {
    return this.alertingService.getAlertRules();
  }

  /**
   * 手动解决告警
   */
  @Post('alerts/:ruleId/resolve')
  @ApiOperation({ summary: '手动解决告警' })
  @ApiResponse({ status: 200, description: '告警解决结果' })
  async resolveAlert(@Param('ruleId') ruleId: string) {
    const resolved = this.alertingService.manualResolveAlert(ruleId);
    return { success: resolved, message: resolved ? '告警已解决' : '告警不存在或已解决' };
  }

  /**
   * 获取链路追踪统计
   */
  @Get('tracing/stats')
  @ApiOperation({ summary: '获取链路追踪统计' })
  @ApiResponse({ status: 200, description: '链路追踪统计信息' })
  async getTracingStats() {
    return this.tracingService.getTracingStats();
  }

  /**
   * 获取活跃追踪
   */
  @Get('tracing/active')
  @ApiOperation({ summary: '获取活跃追踪' })
  @ApiResponse({ status: 200, description: '当前活跃的追踪列表' })
  async getActiveTraces() {
    return this.tracingService.getActiveTraces();
  }

  /**
   * 获取完成的追踪
   */
  @Get('tracing/completed')
  @ApiOperation({ summary: '获取完成的追踪' })
  @ApiResponse({ status: 200, description: '已完成的追踪列表' })
  async getCompletedTraces(@Query('traceId') traceId?: string) {
    return this.tracingService.getCompletedTraces(traceId);
  }

  /**
   * 获取监控仪表板数据
   */
  @Get('dashboard')
  @ApiOperation({ summary: '获取监控仪表板数据' })
  @ApiResponse({ status: 200, description: '监控仪表板综合数据' })
  async getDashboardData() {
    const [metricsStats, activeAlerts, tracingStats] = await Promise.all([
      this.getGatewayStats(),
      this.getActiveAlerts(),
      this.getTracingStats(),
    ]);

    return {
      metrics: metricsStats,
      alerts: {
        active: activeAlerts,
        count: activeAlerts.length,
      },
      tracing: tracingStats,
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * 获取系统健康状态
   */
  @Get('health')
  @ApiOperation({ summary: '获取系统健康状态' })
  @ApiResponse({ status: 200, description: '系统健康状态' })
  async getSystemHealth() {
    const systemMetrics = this.metricsService.getSystemMetrics();
    const activeAlerts = this.alertingService.getActiveAlerts();
    const criticalAlerts = activeAlerts.filter(alert => alert.severity === 'critical');

    const isHealthy = criticalAlerts.length === 0 && systemMetrics.memoryUsage < 0.9;

    return {
      status: isHealthy ? 'healthy' : 'unhealthy',
      checks: {
        memory: {
          status: systemMetrics.memoryUsage < 0.9 ? 'pass' : 'fail',
          usage: systemMetrics.memoryUsage,
          threshold: 0.9,
        },
        cpu: {
          status: systemMetrics.cpuUsage < 0.9 ? 'pass' : 'fail',
          usage: systemMetrics.cpuUsage,
          threshold: 0.9,
        },
        alerts: {
          status: criticalAlerts.length === 0 ? 'pass' : 'fail',
          criticalCount: criticalAlerts.length,
          totalCount: activeAlerts.length,
        },
      },
      timestamp: new Date().toISOString(),
    };
  }

  // ==================== 新增优化方法 ====================

  /**
   * 网关统计信息（优化版）
   * 用途：网关运行时统计数据
   * 使用场景：性能监控、容量规划
   */
  @Get('monitoring/stats')
  @ApiOperation({
    summary: '网关统计信息',
    description: '获取网关运行时统计数据'
  })
  @ApiResponse({
    status: 200,
    description: '网关统计数据'
  })
  async getGatewayStats() {
    try {
      const stats = await this.getGatewayStatsInternal();
      const configStats = this.configManagerService?.getSnapshots()?.length || 0;

      return {
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        requests: {
          total: stats.totalRequests || 0,
          rate: `${((stats.totalRequests || 0) / process.uptime()).toFixed(1)}/sec`,
          errors: stats.errorCount || 0,
          errorRate: stats.errorRate ? `${stats.errorRate.toFixed(2)}%` : '0.00%',
        },
        connections: {
          active: stats.activeConnections || 0,
          total: stats.totalConnections || 0,
        },
        config: {
          snapshots: configStats,
        }
      };
    } catch (error) {
      return {
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        error: '统计数据获取失败',
        message: error.message,
      };
    }
  }

  /**
   * 系统资源信息
   * 用途：系统资源使用情况
   * 使用场景：资源监控、性能调优
   */
  @Get('monitoring/system')
  @ApiOperation({
    summary: '系统资源信息',
    description: '获取系统资源使用情况'
  })
  @ApiResponse({
    status: 200,
    description: '系统资源数据'
  })
  async getSystemInfo() {
    try {
      const memoryUsage = process.memoryUsage();
      const cpuUsage = process.cpuUsage();

      return {
        timestamp: new Date().toISOString(),
        system: {
          cpu: {
            user: cpuUsage.user,
            system: cpuUsage.system,
            cores: require('os').cpus().length,
          },
          memory: {
            used: memoryUsage.heapUsed,
            total: memoryUsage.heapTotal,
            usage: ((memoryUsage.heapUsed / memoryUsage.heapTotal) * 100).toFixed(1),
            heap: {
              used: memoryUsage.heapUsed,
              total: memoryUsage.heapTotal,
            },
            external: memoryUsage.external,
          },
          platform: process.platform,
          arch: process.arch,
        },
        process: {
          pid: process.pid,
          uptime: process.uptime(),
          version: process.version,
          nodeVersion: process.versions.node,
        },
        environment: {
          nodeEnv: process.env.NODE_ENV || 'development',
        }
      };
    } catch (error) {
      return {
        timestamp: new Date().toISOString(),
        error: '系统信息获取失败',
        message: error.message,
      };
    }
  }

  /**
   * 内部方法：获取网关统计数据
   * 兼容现有的MetricsService接口
   */
  private async getGatewayStatsInternal(): Promise<any> {
    try {
      return {
        totalRequests: await this.metricsService.getTotalRequests(),
        averageResponseTime: await this.metricsService.getAverageResponseTime(),
        errorRate: await this.metricsService.getErrorRate(),
        activeConnections: await this.metricsService.getActiveConnections(),
        systemMetrics: this.metricsService.getSystemMetrics(),
        // 添加缺失的属性
        errorCount: 0,
        totalConnections: 0,
      };
    } catch (error) {
      // 如果现有服务不可用，返回基础数据
      return {
        totalRequests: 0,
        averageResponseTime: 0,
        errorRate: 0,
        activeConnections: 0,
        systemMetrics: {},
        errorCount: 0,
        totalConnections: 0,
      };
    }
  }
}
