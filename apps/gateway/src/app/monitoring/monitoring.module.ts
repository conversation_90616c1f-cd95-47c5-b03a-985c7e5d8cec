import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';

// 导入基础设施层监控服务
import { MetricsService } from '../../infrastructure/monitoring/metrics.service';
import { AlertingService } from '../../infrastructure/monitoring/alerting.service';
import { TracingService } from '../../infrastructure/monitoring/tracing.service';
import { ConfigManagerService } from '../../infrastructure/config/config-manager.service';

// 导入控制器
import { MonitoringController } from './monitoring.controller';

/**
 * 监控模块
 *
 * 统一的监控解决方案，包含：
 * - 基础设施层服务：技术实现（MetricsService, AlertingService, TracingService）
 * - 控制器：API暴露（MonitoringController）
 *
 * 功能包括：
 * - Prometheus 指标收集和暴露
 * - 分布式链路追踪
 * - 告警和通知机制
 * - 监控API端点
 * - 监控仪表板支持
 *
 * 架构优势：
 * - 单一模块，避免命名冲突
 * - 职责清晰，便于维护
 * - 统一管理监控相关功能
 */
@Module({
  imports: [ConfigModule],
  providers: [
    // 基础设施层服务
    MetricsService,
    AlertingService,
    TracingService,
    ConfigManagerService,
  ],
  controllers: [
    // 监控API控制器
    MonitoringController,
  ],
  exports: [
    // 导出基础设施层服务供其他模块使用
    MetricsService,
    AlertingService,
    TracingService,
  ],
})
export class MonitoringModule {}
