import { Module } from '@nestjs/common';

// 导入负载均衡服务
import { LoadBalancerService } from './load-balancer.service';

/**
 * 负载均衡模块
 * 
 * 提供网关负载均衡核心功能，包括：
 * - 多种负载均衡算法（轮询、权重、最少连接等）
 * - 服务实例健康检查
 * - 动态权重调整
 * - 故障实例自动剔除
 * 
 * 职责范围：
 * - 在多个后端服务实例间分发请求
 * - 监控服务实例的健康状态
 * - 根据负载情况动态调整权重
 * - 实现故障转移和自动恢复
 */
@Module({
  providers: [
    LoadBalancerService,
  ],
  exports: [
    LoadBalancerService,
  ],
})
export class LoadBalancerModule {}
