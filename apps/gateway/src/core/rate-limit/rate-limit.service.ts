import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { RedisService } from '@common/redis';

export interface RateLimitConfig {
  windowMs: number;
  max: number;
  skipSuccessfulRequests?: boolean;
  skipFailedRequests?: boolean;
  keyGenerator?: (req: any) => string;
  onLimitReached?: (req: any) => void;
  strategy: 'sliding-window' | 'fixed-window' | 'token-bucket' | 'leaky-bucket';
}

export interface RateLimitResult {
  allowed: boolean;
  limit: number;
  remaining: number;
  resetTime: Date;
  retryAfter?: number;
  totalHits: number;
}

export interface TokenBucketConfig extends RateLimitConfig {
  capacity: number;
  refillRate: number;
  refillPeriod: number;
}

export interface LeakyBucketConfig extends RateLimitConfig {
  capacity: number;
  leakRate: number;
  leakPeriod: number;
}

@Injectable()
export class RateLimitService {
  private readonly logger = new Logger(RateLimitService.name);
  private readonly defaultConfig: RateLimitConfig;

  constructor(
    private readonly configService: ConfigService,
    private readonly redisService: RedisService,
  ) {
    this.defaultConfig = {
      windowMs: this.configService.get<number>('gateway.security.rateLimitWindowMs', 60000),
      max: this.configService.get<number>('gateway.security.rateLimitMax', 100),
      strategy: 'sliding-window',
    };
  }

  /**
   * 检查速率限制
   */
  async checkRateLimit(
    key: string,
    config: Partial<RateLimitConfig> = {},
  ): Promise<RateLimitResult> {
    const finalConfig = { ...this.defaultConfig, ...config };

    switch (finalConfig.strategy) {
      case 'sliding-window':
        return await this.checkSlidingWindow(key, finalConfig);
      case 'fixed-window':
        return await this.checkFixedWindow(key, finalConfig);
      case 'token-bucket':
        return await this.checkTokenBucket(key, finalConfig as TokenBucketConfig);
      case 'leaky-bucket':
        return await this.checkLeakyBucket(key, finalConfig as LeakyBucketConfig);
      default:
        return await this.checkSlidingWindow(key, finalConfig);
    }
  }

  /**
   * 滑动窗口算法
   */
  private async checkSlidingWindow(
    key: string,
    config: RateLimitConfig,
  ): Promise<RateLimitResult> {
    const now = Date.now();
    const windowStart = now - config.windowMs;
    const redisKey = `rate_limit:sliding:${key}`;

    try {
      // 使用 Lua 脚本保证原子性
      const script = `
        local key = KEYS[1]
        local window_start = tonumber(ARGV[1])
        local now = tonumber(ARGV[2])
        local max_requests = tonumber(ARGV[3])
        local window_ms = tonumber(ARGV[4])
        
        -- 清理过期的请求记录
        redis.call('ZREMRANGEBYSCORE', key, 0, window_start)
        
        -- 获取当前窗口内的请求数
        local current_requests = redis.call('ZCARD', key)
        
        if current_requests < max_requests then
          -- 添加当前请求
          redis.call('ZADD', key, now, now)
          redis.call('EXPIRE', key, math.ceil(window_ms / 1000))
          return {1, current_requests + 1, max_requests - current_requests - 1, now + window_ms}
        else
          -- 获取最早的请求时间
          local oldest = redis.call('ZRANGE', key, 0, 0, 'WITHSCORES')
          local reset_time = now + window_ms
          if #oldest > 0 then
            reset_time = tonumber(oldest[2]) + window_ms
          end
          return {0, current_requests, 0, reset_time}
        end
      `;

      const client = this.redisService.getClient();
      const result = await client.eval(
        script,
        1,
        redisKey,
        windowStart.toString(),
        now.toString(),
        config.max.toString(),
        config.windowMs.toString(),
      ) as number[];

      const allowed = result[0] === 1;
      const totalHits = result[1];
      const remaining = result[2];
      const resetTime = new Date(result[3]);

      return {
        allowed,
        limit: config.max,
        remaining,
        resetTime,
        retryAfter: allowed ? undefined : Math.ceil((resetTime.getTime() - now) / 1000),
        totalHits,
      };
    } catch (error) {
      this.logger.error(`Sliding window rate limit error for key ${key}:`, error);
      // 发生错误时允许请求通过
      return {
        allowed: true,
        limit: config.max,
        remaining: config.max - 1,
        resetTime: new Date(now + config.windowMs),
        totalHits: 1,
      };
    }
  }

  /**
   * 固定窗口算法
   */
  private async checkFixedWindow(
    key: string,
    config: RateLimitConfig,
  ): Promise<RateLimitResult> {
    const now = Date.now();
    const windowStart = Math.floor(now / config.windowMs) * config.windowMs;
    const windowEnd = windowStart + config.windowMs;
    const redisKey = `rate_limit:fixed:${key}:${windowStart}`;

    try {
      const script = `
        local key = KEYS[1]
        local max_requests = tonumber(ARGV[1])
        local ttl = tonumber(ARGV[2])
        
        local current = redis.call('GET', key)
        if current == false then
          current = 0
        else
          current = tonumber(current)
        end
        
        if current < max_requests then
          local new_count = redis.call('INCR', key)
          redis.call('EXPIRE', key, ttl)
          return {1, new_count, max_requests - new_count}
        else
          return {0, current, 0}
        end
      `;

      const ttl = Math.ceil(config.windowMs / 1000);
      const client = this.redisService.getClient();
      const result = await client.eval(
        script,
        1,
        redisKey,
        config.max.toString(),
        ttl.toString(),
      ) as number[];

      const allowed = result[0] === 1;
      const totalHits = result[1];
      const remaining = result[2];

      return {
        allowed,
        limit: config.max,
        remaining,
        resetTime: new Date(windowEnd),
        retryAfter: allowed ? undefined : Math.ceil((windowEnd - now) / 1000),
        totalHits,
      };
    } catch (error) {
      this.logger.error(`Fixed window rate limit error for key ${key}:`, error);
      return {
        allowed: true,
        limit: config.max,
        remaining: config.max - 1,
        resetTime: new Date(windowEnd),
        totalHits: 1,
      };
    }
  }

  /**
   * 令牌桶算法
   */
  private async checkTokenBucket(
    key: string,
    config: TokenBucketConfig,
  ): Promise<RateLimitResult> {
    const now = Date.now();
    const redisKey = `rate_limit:token_bucket:${key}`;

    try {
      const script = `
        local key = KEYS[1]
        local capacity = tonumber(ARGV[1])
        local refill_rate = tonumber(ARGV[2])
        local refill_period = tonumber(ARGV[3])
        local now = tonumber(ARGV[4])
        
        local bucket = redis.call('HMGET', key, 'tokens', 'last_refill')
        local tokens = tonumber(bucket[1]) or capacity
        local last_refill = tonumber(bucket[2]) or now
        
        -- 计算需要添加的令牌数
        local time_passed = now - last_refill
        local tokens_to_add = math.floor(time_passed / refill_period) * refill_rate
        tokens = math.min(capacity, tokens + tokens_to_add)
        
        if tokens >= 1 then
          tokens = tokens - 1
          redis.call('HMSET', key, 'tokens', tokens, 'last_refill', now)
          redis.call('EXPIRE', key, 3600) -- 1小时过期
          return {1, tokens, capacity}
        else
          redis.call('HMSET', key, 'tokens', tokens, 'last_refill', now)
          redis.call('EXPIRE', key, 3600)
          -- 计算下次可用时间
          local next_token_time = now + refill_period
          return {0, tokens, next_token_time}
        end
      `;

      const client = this.redisService.getClient();
      const result = await client.eval(
        script,
        1,
        redisKey,
        config.capacity.toString(),
        config.refillRate.toString(),
        config.refillPeriod.toString(),
        now.toString(),
      ) as number[];

      const allowed = result[0] === 1;
      const remaining = result[1];
      const nextTokenTime = allowed ? now + config.refillPeriod : result[2];

      return {
        allowed,
        limit: config.capacity,
        remaining,
        resetTime: new Date(nextTokenTime),
        retryAfter: allowed ? undefined : Math.ceil((nextTokenTime - now) / 1000),
        totalHits: config.capacity - remaining,
      };
    } catch (error) {
      this.logger.error(`Token bucket rate limit error for key ${key}:`, error);
      return {
        allowed: true,
        limit: config.capacity,
        remaining: config.capacity - 1,
        resetTime: new Date(now + config.refillPeriod),
        totalHits: 1,
      };
    }
  }

  /**
   * 漏桶算法
   */
  private async checkLeakyBucket(
    key: string,
    config: LeakyBucketConfig,
  ): Promise<RateLimitResult> {
    const now = Date.now();
    const redisKey = `rate_limit:leaky_bucket:${key}`;

    try {
      const script = `
        local key = KEYS[1]
        local capacity = tonumber(ARGV[1])
        local leak_rate = tonumber(ARGV[2])
        local leak_period = tonumber(ARGV[3])
        local now = tonumber(ARGV[4])
        
        local bucket = redis.call('HMGET', key, 'volume', 'last_leak')
        local volume = tonumber(bucket[1]) or 0
        local last_leak = tonumber(bucket[2]) or now
        
        -- 计算泄漏量
        local time_passed = now - last_leak
        local leak_amount = math.floor(time_passed / leak_period) * leak_rate
        volume = math.max(0, volume - leak_amount)
        
        if volume < capacity then
          volume = volume + 1
          redis.call('HMSET', key, 'volume', volume, 'last_leak', now)
          redis.call('EXPIRE', key, 3600) -- 1小时过期
          return {1, volume, capacity - volume}
        else
          redis.call('HMSET', key, 'volume', volume, 'last_leak', now)
          redis.call('EXPIRE', key, 3600)
          -- 计算下次可用时间
          local next_available_time = now + leak_period
          return {0, volume, next_available_time}
        end
      `;

      const client = this.redisService.getClient();
      const result = await client.eval(
        script,
        1,
        redisKey,
        config.capacity.toString(),
        config.leakRate.toString(),
        config.leakPeriod.toString(),
        now.toString(),
      ) as number[];

      const allowed = result[0] === 1;
      const volume = result[1];
      const remaining = allowed ? result[2] : 0;
      const nextAvailableTime = allowed ? now + config.leakPeriod : result[2];

      return {
        allowed,
        limit: config.capacity,
        remaining,
        resetTime: new Date(nextAvailableTime),
        retryAfter: allowed ? undefined : Math.ceil((nextAvailableTime - now) / 1000),
        totalHits: volume,
      };
    } catch (error) {
      this.logger.error(`Leaky bucket rate limit error for key ${key}:`, error);
      return {
        allowed: true,
        limit: config.capacity,
        remaining: config.capacity - 1,
        resetTime: new Date(now + config.leakPeriod),
        totalHits: 1,
      };
    }
  }

  /**
   * 生成用户级别的限流键
   */
  generateUserKey(userId: string, endpoint?: string): string {
    return endpoint ? `user:${userId}:${endpoint}` : `user:${userId}`;
  }

  /**
   * 生成 IP 级别的限流键
   */
  generateIpKey(ip: string, endpoint?: string): string {
    return endpoint ? `ip:${ip}:${endpoint}` : `ip:${ip}`;
  }

  /**
   * 生成 API 级别的限流键
   */
  generateApiKey(endpoint: string): string {
    return `api:${endpoint}`;
  }

  /**
   * 生成全局限流键
   */
  generateGlobalKey(): string {
    return 'global';
  }

  /**
   * 获取限流统计信息
   */
  async getRateLimitStats(key: string): Promise<any> {
    const patterns = [
      `rate_limit:sliding:${key}*`,
      `rate_limit:fixed:${key}*`,
      `rate_limit:token_bucket:${key}*`,
      `rate_limit:leaky_bucket:${key}*`,
    ];

    const stats = {
      key,
      strategies: {},
      totalRequests: 0,
      blockedRequests: 0,
    };

    for (const pattern of patterns) {
      const keys = await this.redisService.getClient().keys(pattern);
      // 这里可以添加更详细的统计逻辑
    }

    return stats;
  }

  /**
   * 清除限流数据
   */
  async clearRateLimit(key: string): Promise<void> {
    const patterns = [
      `rate_limit:sliding:${key}*`,
      `rate_limit:fixed:${key}*`,
      `rate_limit:token_bucket:${key}*`,
      `rate_limit:leaky_bucket:${key}*`,
    ];

    for (const pattern of patterns) {
      const keys = await this.redisService.getClient().keys(pattern);
      if (keys.length > 0) {
        await this.redisService.getClient().del(...keys);
      }
    }
  }
}
