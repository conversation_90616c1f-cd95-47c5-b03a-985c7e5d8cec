import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { EventEmitterModule } from '@nestjs/event-emitter';

// 直接导入核心服务（避免复杂的模块依赖）
import { RouteResolverService } from '../router/route-resolver.service';
import { AuthService as CoreAuthService } from '../auth/auth.service';
import { RateLimitService } from '../rate-limit/rate-limit.service';
import { CircuitBreakerService } from '../circuit-breaker/circuit-breaker.service';
import { GatewayCacheService } from '../cache/gateway-cache.service';

// 移除对业务层的直接依赖
// ProxyService 和 GraphQLGatewayService 应该在各自的业务模块中管理

// 从 infrastructure 导入业务服务
import { ServiceDiscoveryService } from '../../infrastructure/discovery/service-discovery.service';
import { LoggingService } from '../../infrastructure/logging/logging.service';
import { ConfigManagerService } from '../../infrastructure/config/config-manager.service';
import { TracingService } from '../../infrastructure/tracing/tracing.service';
import { MetricsService } from '../../infrastructure/monitoring/metrics.service';

// 导入依赖模块
import { JwtSharedModule } from '../../infrastructure/jwt/jwt-shared.module';
import { LoadBalancerModule } from '../load-balancer/load-balancer.module';
// 移除对业务层 AuthModule 的依赖

/**
 * 核心模块
 *
 * 统一管理所有核心服务，解决以下问题：
 * - 消除重复注册（原来在 AppModule 和 ProxyModule 中重复注册）
 * - 统一核心服务管理
 * - 提供单一的核心服务入口
 * - 避免复杂的模块间依赖关系
 *
 * 包含的服务：
 * - 路由服务：RouteResolverService (路由解析器)
 * - 认证服务：AuthService (核心层)
 * - 限流服务：RateLimitService
 * - 负载均衡：LoadBalancerService
 * - 熔断器：CircuitBreakerService
 * - 缓存服务：GatewayCacheService
 * - 业务服务：ServiceDiscoveryService, LoggingService, ConfigManagerService, TracingService
 */
@Module({
  imports: [
    ConfigModule,
    EventEmitterModule.forRoot(),
    JwtSharedModule,
    LoadBalancerModule,
    // 移除对业务层的依赖，保持核心层的独立性
  ],
  providers: [
    // 路由核心服务
    RouteResolverService,

    // 认证核心服务
    CoreAuthService,

    // 限流核心服务
    RateLimitService,

    // 熔断器核心服务
    CircuitBreakerService,

    // 缓存核心服务
    GatewayCacheService,

    // 移除业务层服务，它们应该在各自的业务模块中管理
    // ProxyService 在 ProxyModule 中管理
    // GraphQLGatewayService 在 GraphQLModule 中管理

    // 业务服务
    ServiceDiscoveryService,
    LoggingService,
    ConfigManagerService,
    TracingService,
    MetricsService,
  ],
  exports: [
    // 导出所有核心服务供其他模块使用
    RouteResolverService,
    CoreAuthService,
    RateLimitService,
    LoadBalancerModule,  // 导出模块而不是服务
    CircuitBreakerService,
    GatewayCacheService,
    // 移除业务层服务的导出
    ServiceDiscoveryService,
    LoggingService,
    ConfigManagerService,
    TracingService,
    MetricsService,
  ],
})
export class CoreModule {}
