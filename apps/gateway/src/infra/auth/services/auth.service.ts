import { Injectable, Logger, UnauthorizedException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { RedisService } from '@common/redis';
import { DataType } from '@common/redis/types/redis.types';
import { MicroserviceClientService } from '@common/microservice-kit';
import { MICROSERVICE_NAMES } from '@libs/shared';
import * as crypto from 'crypto';

// 验证结果接口 - 与Auth服务完全一致
interface ValidationResult {
  valid: boolean;
  user?: {
    id: string;
    username: string;
    email: string;
    roles: string[];
    permissions: string[];
    status: string;
    emailVerified: boolean;
    profile?: {
      firstName: string;
      lastName: string;
      language: string;
    };
    security: {
      mfaEnabled: boolean;
      lastPasswordChange?: Date;
    };
  };
  token?: {
    jti: string;
    iat: number;
    exp: number;
    expiresIn: number;
    type: string;
  };
  character?: {
    characterId: string;
    serverId: string;
    characterName?: string;
  };
  tokenScope?: 'account' | 'character';
  error?: string;
  errorCode?: string;
}

@Injectable()
export class AuthService {
  private readonly logger = new Logger(AuthService.name);

  constructor(
    private readonly configService: ConfigService,
    private readonly redisService: RedisService,
    private readonly microserviceClient: MicroserviceClientService,
  ) {}

  /**
   * 代理验证Token - 带缓存机制
   */
  async validateToken(token: string): Promise<ValidationResult> {
    if (!token) {
      return { valid: false, error: 'Token不能为空' };
    }

    // 1. 检查缓存
    const cacheKey = `token_validation:${this.hashToken(token)}`;
    this.logger.debug(`🔍 缓存键: ${cacheKey}`);

    const cached = await this.redisService.get(cacheKey, 'global');
    this.logger.debug(`🔍 缓存查询结果: ${cached ? '命中' : '未命中'}`);
    this.logger.debug(`🔍 缓存值类型: ${typeof cached}`);
    this.logger.debug(`🔍 缓存值内容: ${cached ? JSON.stringify(cached).substring(0, 100) + '...' : 'null'}`);

    if (cached && typeof cached === 'string') {
      this.logger.debug('✅ Token验证缓存命中（字符串格式）');
      return JSON.parse(cached) as ValidationResult;
    }

    if (cached && typeof cached === 'object') {
      this.logger.debug('✅ Token验证缓存命中（对象格式）');
      return cached as ValidationResult;
    }

    try {
      // 2. 调用Auth服务的@MessagePattern方法
      this.logger.debug(`调用Auth服务验证Token: ${token.substring(0, 8)}***`);
      const result = await this.microserviceClient.call(
        MICROSERVICE_NAMES.AUTH_SERVICE,
        'auth.verifyToken',  // 使用正确的MessagePattern
        { token }
      );

      if (!result.success) {
        this.logger.warn(`Token验证失败: ${result.error}`);
        return { valid: false, error: result.error || 'Token验证失败' };
      }

      // 3. 缓存验证结果（5分钟）
      this.logger.debug(`🔍 准备缓存验证结果，键: ${cacheKey}`);
      this.logger.debug(`🔍 缓存数据: ${JSON.stringify(result.data).substring(0, 100)}...`);

      await this.redisService.set(cacheKey, JSON.stringify(result.data), 300, 'global');

      // 验证缓存是否写入成功
      const verifyCache = await this.redisService.get(cacheKey, 'global');
      this.logger.debug(`🔍 缓存写入验证: ${verifyCache ? '成功' : '失败'}`);

      this.logger.debug('✅ Token验证成功并缓存');
      return result.data;
    } catch (error) {
      this.logger.error(`Token验证异常: ${error.message}`, error.stack);
      return { valid: false, error: 'Token验证服务异常' };
    }
  }

  /**
   * 用户信息注入到请求上下文
   */
  injectUserContext(request: any, result: ValidationResult): void {
    if (result.valid && result.user) {
      request.user = {
        id: result.user.id,
        username: result.user.username,
        email: result.user.email,
        roles: result.user.roles,
        permissions: result.user.permissions,
        level: result.user.level,
        tokenScope: result.scope,
      };
    }
  }

  /**
   * 从请求中提取Token
   */
  extractTokenFromRequest(request: any): string | null {
    const authHeader = request.headers.authorization;
    if (authHeader && authHeader.startsWith('Bearer ')) {
      return authHeader.substring(7);
    }
    return null;
  }

  /**
   * 代理权限检查 - 调用Auth服务
   */
  async checkPermission(userId: string, resource: string, action: string): Promise<boolean> {
    try {
      const result = await this.microserviceClient.call(
        MICROSERVICE_NAMES.AUTH_SERVICE,
        'auth.checkPermission',
        { userId, resource, action }
      );

      return result.success && result.data?.hasPermission;
    } catch (error) {
      this.logger.error('权限检查失败', error);
      return false;
    }
  }

  /**
   * 代理API Key验证 - 调用Auth服务的ApiKey模块
   */
  async validateApiKey(apiKey: string, clientIp?: string, userAgent?: string): Promise<ValidationResult> {
    try {
      const result = await this.microserviceClient.call(
        MICROSERVICE_NAMES.AUTH_SERVICE,
        'validateApiKey',
        { apiKey, clientIp, userAgent }
      );

      if (result.success) {
        return {
          valid: true,
          user: {
            id: result.data.id,
            username: result.data.name,
            email: '',
            roles: [],
            permissions: result.data.permissions || [],
          },
          scope: result.data.permissions || []
        };
      }

      return { valid: false, error: result.error || 'API Key验证失败' };
    } catch (error) {
      this.logger.error('API Key验证失败', error);
      return { valid: false, error: 'API Key验证失败' };
    }
  }

  /**
   * 清除Token缓存 - 用于登出时清理本地缓存
   * 注意：实际的登出操作通过HTTP路由转发到Auth服务
   */
  async clearTokenCache(token: string): Promise<void> {
    try {
      const cacheKey = `token_validation:${this.hashToken(token)}`;
      await this.redisService.del(cacheKey, 'global');
      this.logger.debug(`Token缓存已清除: ${cacheKey}`);
    } catch (error) {
      this.logger.error('清除Token缓存失败', error);
    }
  }

  /**
   * Token哈希（用于缓存键）
   */
  private hashToken(token: string): string {
    return crypto.createHash('sha256').update(token).digest('hex').substring(0, 16);
  }
}
