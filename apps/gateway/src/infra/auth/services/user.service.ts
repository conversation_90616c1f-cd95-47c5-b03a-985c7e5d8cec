import { Injectable, Logger } from '@nestjs/common';

/**
 * 用户认证辅助服务
 *
 * 注意：根据架构决策，用户CRUD操作（登录、注册、密码修改等）
 * 应该通过HTTP路由转发直接代理到Auth服务，而不是在Gateway层进行微服务调用。
 *
 * 此服务保留用于未来可能需要的用户认证相关的辅助功能，
 * 目前主要的认证功能由AuthService提供。
 */
@Injectable()
export class UserService {
  private readonly logger = new Logger(UserService.name);

  constructor() {
    this.logger.log('UserService initialized - CRUD operations handled by HTTP routing');
  }

  // 注意：用户登录、注册、密码修改等操作已移除
  // 这些操作现在通过Gateway的路由转发直接代理到Auth服务的HTTP API
  // 例如：
  // POST /api/auth/login -> 直接转发到 Auth服务的 /api/auth/login
  // POST /api/auth/register -> 直接转发到 Auth服务的 /api/auth/register
  // POST /api/auth/change-password -> 直接转发到 Auth服务的 /api/auth/change-password
}
