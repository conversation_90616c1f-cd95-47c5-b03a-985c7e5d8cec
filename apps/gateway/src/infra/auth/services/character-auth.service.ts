import { Injectable, Logger } from '@nestjs/common';
import { MicroserviceClientService } from '@common/microservice-kit';
import { MICROSERVICE_NAMES } from '@shared/constants';

/**
 * 角色认证服务
 * 
 * 负责角色相关的认证功能，包括：
 * - 角色Token生成
 * - 角色登录处理
 * - 角色会话管理
 */
@Injectable()
export class CharacterAuthService {
  private readonly logger = new Logger(CharacterAuthService.name);

  constructor(
    private readonly microserviceClient: MicroserviceClientService,
  ) {}

  /**
   * 生成角色Token - 调用Auth服务
   */
  async generateCharacterToken(request: {
    userId: string;
    characterId: string;
    serverId: string;
    characterName: string;
  }): Promise<any> {
    try {
      this.logger.log(`🔑 生成角色Token: userId=${request.userId}, characterId=${request.characterId}`);

      const tokenResult = await this.microserviceClient.call(
        MICROSERVICE_NAMES.AUTH_SERVICE,
        'character-auth.generateToken',
        request
      );

      if (!tokenResult.success) {
        throw new Error(tokenResult.error || '角色Token生成失败');
      }

      this.logger.log(`✅ 角色Token生成成功: characterId=${request.characterId}`);
      return tokenResult;
    } catch (error) {
      this.logger.error('角色Token生成失败', error);
      throw error;
    }
  }

  /**
   * 验证角色Token - 调用Auth服务
   */
  async validateCharacterToken(token: string): Promise<any> {
    try {
      const result = await this.microserviceClient.call(
        MICROSERVICE_NAMES.AUTH_SERVICE,
        'character-auth.verifyToken',
        { token }
      );

      if (!result.success) {
        throw new Error(result.error || '角色Token验证失败');
      }

      return result.data;
    } catch (error) {
      this.logger.error('角色Token验证失败', error);
      throw error;
    }
  }

  /**
   * 角色登出 - 调用Auth服务
   */
  async logoutCharacter(userId: string, characterId: string, serverId: string): Promise<void> {
    try {
      const result = await this.microserviceClient.call(
        MICROSERVICE_NAMES.AUTH_SERVICE,
        'character-auth.logout',
        { userId, characterId, serverId }
      );

      if (!result.success) {
        throw new Error(result.error || '角色登出失败');
      }

      this.logger.log(`✅ 角色登出成功: characterId=${characterId}`);
    } catch (error) {
      this.logger.error('角色登出失败', error);
      throw error;
    }
  }
}
