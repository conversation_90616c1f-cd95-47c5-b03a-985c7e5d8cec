import { Injectable, Logger } from '@nestjs/common';
import { MicroserviceClientService } from '@common/microservice-kit';
import { MICROSERVICE_NAMES } from '@shared/constants';

/**
 * 角色认证服务
 * 
 * 负责角色相关的认证功能，包括：
 * - 角色Token生成
 * - 角色登录处理
 * - 角色会话管理
 */
@Injectable()
export class CharacterAuthService {
  private readonly logger = new Logger(CharacterAuthService.name);

  constructor(
    private readonly microserviceClient: MicroserviceClientService,
  ) {}

  /**
   * 生成角色Token - 调用Auth服务
   */
  async generateCharacterToken(request: {
    userId: string;
    characterId: string;
    serverId: string;
    characterName: string;
  }): Promise<any> {
    try {
      this.logger.log(`🔑 生成角色Token: userId=${request.userId}, characterId=${request.characterId}`);

      const tokenResult = await this.microserviceClient.call(
        MICROSERVICE_NAMES.AUTH_SERVICE,
        'character-auth.generateCharacterToken',
        request
      );

      if (!tokenResult.success) {
        throw new Error(tokenResult.error || '角色Token生成失败');
      }

      this.logger.log(`✅ 角色Token生成成功: characterId=${request.characterId}`);
      return tokenResult;
    } catch (error) {
      this.logger.error('角色Token生成失败', error);
      throw error;
    }
  }

  /**
   * 验证角色Token - 调用Auth服务
   */
  async validateCharacterToken(token: string): Promise<{
    valid: boolean;
    user?: any;
    character?: any;
    tokenScope?: string;
    error?: string;
  }> {
    try {
      this.logger.debug(`🔑 验证角色Token: ${token.substring(0, 8)}***`);

      const result = await this.microserviceClient.call(
        MICROSERVICE_NAMES.AUTH_SERVICE,
        'character-auth.verifyCharacterToken',
        { characterToken: token }
      );

      if (!result.valid) {
        this.logger.warn(`❌ 角色Token验证失败: ${result.error}`);
        return {
          valid: false,
          error: result.error || '角色Token验证失败'
        };
      }

      this.logger.debug(`✅ 角色Token验证成功: userId=${result.data.userId}, characterId=${result.data.characterId}`);

      // 根据Auth服务的实际返回格式构建响应
      return {
        valid: true,
        user: {
          id: result.data.userId,
          username: result.data.characterName,
          // 其他用户信息需要从Auth服务获取
        },
        character: {
          characterId: result.data.characterId,
          serverId: result.data.serverId,
          characterName: result.data.characterName,
        },
        tokenScope: 'character'
      };
    } catch (error) {
      this.logger.error('角色Token验证异常', error);
      return {
        valid: false,
        error: '角色Token验证服务异常'
      };
    }
  }

  /**
   * 角色登出 - 调用Auth服务
   */
  async logoutCharacter(userId: string, characterId: string, serverId: string): Promise<void> {
    try {
      const result = await this.microserviceClient.call(
        MICROSERVICE_NAMES.AUTH_SERVICE,
        'character-auth.logout',
        { userId, characterId, serverId }
      );

      if (!result.success) {
        throw new Error(result.error || '角色登出失败');
      }

      this.logger.log(`✅ 角色登出成功: characterId=${characterId}`);
    } catch (error) {
      this.logger.error('角色登出失败', error);
      throw error;
    }
  }
}
