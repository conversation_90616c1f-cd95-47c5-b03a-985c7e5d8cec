import { IsString, <PERSON>NotEmpty, <PERSON><PERSON><PERSON><PERSON>, IsOptional } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

/**
 * 登录请求 DTO
 */
export class LoginDto {
  @ApiProperty({
    description: '用户名或邮箱',
    example: 'john_doe',
  })
  @IsString()
  @IsNotEmpty()
  username: string;

  @ApiProperty({
    description: '密码',
    example: 'password123',
    minLength: 6,
  })
  @IsString()
  @IsNotEmpty()
  @MinLength(6)
  password: string;

  @ApiProperty({
    description: '记住登录状态',
    example: false,
    required: false,
  })
  @IsOptional()
  rememberMe?: boolean;
}
