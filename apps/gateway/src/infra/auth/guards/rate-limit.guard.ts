import { Injectable, CanActivate, ExecutionContext, HttpException, HttpStatus } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { RateLimitService } from '../../../modules/rate-limiting/services/rate-limit.service';

/**
 * 限流守卫
 * 
 * 对请求进行限流控制，防止滥用和攻击
 */
@Injectable()
export class RateLimitGuard implements CanActivate {
  constructor(
    private readonly rateLimitService: RateLimitService,
    private readonly reflector: Reflector,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    
    // 检查是否跳过限流
    const skipRateLimit = this.reflector.getAllAndOverride<boolean>('skipRateLimit', [
      context.getHandler(),
      context.getClass(),
    ]);

    if (skipRateLimit) {
      return true;
    }

    // 获取限流配置
    const rateLimitConfig = this.reflector.getAllAndOverride('rateLimitConfig', [
      context.getHandler(),
      context.getClass(),
    ]) || {
      windowMs: 60000, // 1分钟
      max: 100, // 最多100个请求
    };

    // 构建限流键
    const key = this.buildRateLimitKey(request);
    
    try {
      const result = await this.rateLimitService.checkRateLimit(key, rateLimitConfig);
      
      if (!result.allowed) {
        throw new HttpException(
          {
            statusCode: HttpStatus.TOO_MANY_REQUESTS,
            message: 'Too many requests',
            retryAfter: result.retryAfter,
          },
          HttpStatus.TOO_MANY_REQUESTS,
        );
      }

      // 添加限流信息到响应头
      const response = context.switchToHttp().getResponse();
      response.setHeader('X-RateLimit-Limit', rateLimitConfig.max);
      response.setHeader('X-RateLimit-Remaining', result.remaining);
      response.setHeader('X-RateLimit-Reset', result.resetTime);

      return true;
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      
      // 在错误情况下允许访问，避免阻塞正常用户
      return true;
    }
  }

  /**
   * 构建限流键
   */
  private buildRateLimitKey(request: any): string {
    const userId = request.user?.id;
    const ip = this.getClientIp(request);
    
    // 优先使用用户ID，其次使用IP地址
    return userId ? `user:${userId}` : `ip:${ip}`;
  }

  /**
   * 获取客户端IP地址
   */
  private getClientIp(request: any): string {
    const forwarded = request.headers['x-forwarded-for'];
    if (forwarded) {
      const forwardedStr = Array.isArray(forwarded) ? forwarded[0] : forwarded;
      return forwardedStr.split(',')[0].trim();
    }
    
    const realIp = request.headers['x-real-ip'];
    if (realIp && typeof realIp === 'string') {
      return realIp;
    }
    
    return request.connection?.remoteAddress || 
           request.socket?.remoteAddress || 
           'unknown';
  }
}
