import { Injectable, CanActivate, ExecutionContext, Logger } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { JwtService } from '@nestjs/jwt';
import { WsException } from '@nestjs/websockets';
import { Socket } from 'socket.io';
import { AuthService } from '../services/auth.service';
import { CharacterAuthService } from '../services/character-auth.service';

/**
 * WebSocket 认证守卫
 *
 * 验证 WebSocket 连接的认证状态，使用统一的AuthService进行Token验证
 */
@Injectable()
export class WsAuthGuard implements CanActivate {
  private readonly logger = new Logger(WsAuthGuard.name);

  constructor(
    private readonly authService: AuthService,
    private readonly characterAuthService: CharacterAuthService,
    private readonly jwtService: JwtService,
    private readonly reflector: Reflector,
  ) {}

  /**
   * 验证 WebSocket 连接的认证状态
   */
  async canActivate(context: ExecutionContext): Promise<boolean> {
    this.logger.debug(`🚪 WebSocket认证守卫开始执行...`);

    try {
      const client: Socket = context.switchToWs().getClient();
      this.logger.debug(`🚪 获取到WebSocket客户端: ${client.id}`);

      // 检查是否为公开事件
      const isPublic = this.reflector.getAllAndOverride<boolean>('isPublic', [
        context.getHandler(),
        context.getClass(),
      ]);

      if (isPublic) {
        this.logger.debug(`🚪 公开事件，跳过认证`);
        return true;
      }

      this.logger.debug(`🚪 私有事件，需要认证`);
      const token = this.extractTokenFromSocket(client);

      if (!token) {
        this.logger.warn(`❌ WebSocket认证失败: 未提供Token`);
        this.logger.warn(`🔍 握手信息调试:`);
        this.logger.warn(`   - auth对象: ${JSON.stringify(client.handshake.auth)}`);
        this.logger.warn(`   - query参数: ${JSON.stringify(client.handshake.query)}`);
        this.logger.warn(`   - authorization头: ${client.handshake.headers?.authorization}`);
        throw new WsException('Authentication required');
      }

      this.logger.debug(`✅ Token提取成功，开始双Token验证...`);

      // 验证双Token：根据Token类型调用对应的服务
      const validationResult = await this.validateDualToken(token);

      // 将用户信息附加到 socket 对象
      client.data.user = {
        id: validationResult.user.id,
        username: validationResult.user.username,
        email: validationResult.user.email,
        roles: validationResult.user.roles || [],
        permissions: validationResult.user.permissions || [],
        tokenScope: validationResult.tokenScope,
      };

      // 如果是角色Token，附加角色上下文
      if (validationResult.character) {
        client.data.character = validationResult.character;
      }

      // 检查Token作用域
      const requiredScope = this.reflector.get<'account' | 'character'>(
        'TOKEN_SCOPE_KEY',
        context.getHandler()
      );

      if (requiredScope && validationResult.tokenScope !== requiredScope) {
        this.logger.warn(`❌ Token作用域不匹配，期望: ${requiredScope}, 实际: ${validationResult.tokenScope}`);
        throw new WsException(`Token作用域不匹配，期望: ${requiredScope}, 实际: ${validationResult.tokenScope}`);
      }

      // 检查所需权限
      const requiredRoles = this.reflector.getAllAndOverride<string[]>('roles', [
        context.getHandler(),
        context.getClass(),
      ]);

      if (requiredRoles && requiredRoles.length > 0) {
        const userRoles = result.user.roles || [];
        const hasRequiredRole = requiredRoles.some(role => userRoles.includes(role));

        if (!hasRequiredRole) {
          this.logger.warn(`❌ 权限不足，需要角色: ${requiredRoles.join(', ')}, 用户角色: ${userRoles.join(', ')}`);
          throw new WsException('Insufficient permissions');
        }
      }

      this.logger.debug(`✅ WebSocket authentication successful for user: ${result.user.id}`);
      return true;

    } catch (error) {
      this.logger.error(`❌ WebSocket认证过程出错: ${error.message}`);
      if (error instanceof WsException) {
        throw error;
      }
      throw new WsException('Authentication failed');
    }
  }

  /**
   * 从 Socket 连接中提取 JWT 令牌
   */
  private extractTokenFromSocket(client: Socket): string | null {
    this.logger.debug(`🔍 开始提取WebSocket Token...`);

    // 方法1: 从 auth 对象中获取（推荐方式）
    const authToken = client.handshake.auth?.token;
    this.logger.debug(`🔍 Auth对象Token: ${authToken ? '存在' : '不存在'}`);
    if (authToken && typeof authToken === 'string') {
      this.logger.debug(`✅ 从auth对象获取到Token，长度: ${authToken.length}`);
      return authToken;
    }

    // 方法2: 从查询参数中获取
    const queryToken = client.handshake.query?.token;
    this.logger.debug(`🔍 查询参数Token: ${queryToken ? '存在' : '不存在'}`);
    if (queryToken && typeof queryToken === 'string') {
      this.logger.debug(`✅ 从查询参数获取到Token，长度: ${queryToken.length}`);
      return queryToken;
    }

    // 方法3: 从 Authorization 头中获取
    const authHeader = client.handshake.headers?.authorization;
    this.logger.debug(`🔍 Authorization头: ${authHeader ? '存在' : '不存在'}`);
    if (authHeader && typeof authHeader === 'string' && authHeader.startsWith('Bearer ')) {
      const headerToken = authHeader.substring(7);
      this.logger.debug(`✅ 从Authorization头获取到Token，长度: ${headerToken.length}`);
      return headerToken;
    }

    this.logger.warn(`❌ 未能从任何来源提取到Token`);
    return null;
  }

  /**
   * 验证双Token：根据Token类型调用对应的服务
   * 支持账号Token和角色Token的验证
   */
  private async validateDualToken(token: string): Promise<{
    user: any;
    character?: any;
    tokenScope: 'account' | 'character';
  }> {
    this.logger.debug(`🔐 开始验证Token，长度: ${token.length}`);

    try {
      // 首先解析Token获取基本信息（不验证签名）
      this.logger.debug(`🔐 步骤1: 解析Token基本信息...`);
      const decoded = this.jwtService.decode(token) as any;

      if (!decoded) {
        this.logger.error(`❌ Token解析失败: 无法解码Token`);
        throw new WsException('Invalid token format');
      }

      this.logger.debug(`🔐 Token解析成功，scope: ${decoded.scope}, sub: ${decoded.sub}`);

      if (!decoded.scope) {
        this.logger.error(`❌ Token验证失败: 缺少scope字段`);
        throw new WsException('Invalid token format');
      }

      // 根据scope字段选择验证服务
      this.logger.debug(`🔐 步骤2: 根据scope调用对应服务验证Token...`);
      switch (decoded.scope) {
        case 'account':
          this.logger.debug(`🔐 使用AuthService验证账号Token`);
          return await this.validateAccountTokenViaService(token);
        case 'character':
          this.logger.debug(`🔐 使用CharacterAuthService验证角色Token`);
          return await this.validateCharacterTokenViaService(token);
        default:
          this.logger.error(`❌ 不支持的Token scope: ${decoded.scope}`);
          throw new WsException(`Unsupported token scope: ${decoded.scope}`);
      }

    } catch (error) {
      this.logger.error(`❌ Token验证过程出错:`, error.message);
      if (error instanceof WsException) {
        throw error;
      }
      throw new WsException('Token validation failed');
    }
  }

  /**
   * 通过AuthService验证账号Token
   */
  private async validateAccountTokenViaService(token: string): Promise<{
    user: any;
    tokenScope: 'account';
  }> {
    this.logger.debug(`🔑 通过AuthService验证账号Token...`);

    const result = await this.authService.validateToken(token);

    if (!result.valid) {
      this.logger.warn(`❌ 账号Token验证失败: ${result.error}`);
      throw new WsException(result.error || '账号Token验证失败');
    }

    // 验证Token作用域
    if (result.user?.tokenScope !== 'account') {
      this.logger.error(`❌ Token scope验证失败: 期望'account'，实际'${result.user?.tokenScope}'`);
      throw new WsException('Expected account token');
    }

    this.logger.debug(`✅ 账号Token验证成功，用户: ${result.user.id}`);
    return {
      user: result.user,
      tokenScope: 'account',
    };
  }

  /**
   * 通过CharacterAuthService验证角色Token
   */
  private async validateCharacterTokenViaService(token: string): Promise<{
    user: any;
    character: any;
    tokenScope: 'character';
  }> {
    this.logger.debug(`🔑 通过CharacterAuthService验证角色Token...`);

    const result = await this.characterAuthService.validateCharacterToken(token);

    if (!result.valid) {
      this.logger.warn(`❌ 角色Token验证失败: ${result.error}`);
      throw new WsException(result.error || '角色Token验证失败');
    }

    // 验证Token作用域
    if (result.tokenScope !== 'character') {
      this.logger.error(`❌ Token scope验证失败: 期望'character'，实际'${result.tokenScope}'`);
      throw new WsException('Expected character token');
    }

    // 验证角色上下文
    if (!result.character?.characterId || !result.character?.serverId) {
      this.logger.error(`❌ 角色Token缺少必要的角色上下文信息`);
      throw new WsException('Character token must contain character and server information');
    }

    this.logger.debug(`✅ 角色Token验证成功，用户: ${result.user.id}, 角色: ${result.character.characterId}`);
    return {
      user: result.user,
      character: result.character,
      tokenScope: 'character',
    };
  }
}
