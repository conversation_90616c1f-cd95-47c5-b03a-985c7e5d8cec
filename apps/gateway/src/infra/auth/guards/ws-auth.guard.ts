import { Injectable, CanActivate, ExecutionContext, Logger } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { WsException } from '@nestjs/websockets';
import { Socket } from 'socket.io';
import { AuthService } from '../services/auth.service';

/**
 * WebSocket 认证守卫
 *
 * 验证 WebSocket 连接的认证状态，使用统一的AuthService进行Token验证
 */
@Injectable()
export class WsAuthGuard implements CanActivate {
  private readonly logger = new Logger(WsAuthGuard.name);

  constructor(
    private readonly authService: AuthService,
    private readonly reflector: Reflector,
  ) {}

  /**
   * 验证 WebSocket 连接的认证状态
   */
  async canActivate(context: ExecutionContext): Promise<boolean> {
    this.logger.debug(`🚪 WebSocket认证守卫开始执行...`);

    try {
      const client: Socket = context.switchToWs().getClient();
      this.logger.debug(`🚪 获取到WebSocket客户端: ${client.id}`);

      // 检查是否为公开事件
      const isPublic = this.reflector.getAllAndOverride<boolean>('isPublic', [
        context.getHandler(),
        context.getClass(),
      ]);

      if (isPublic) {
        this.logger.debug(`🚪 公开事件，跳过认证`);
        return true;
      }

      this.logger.debug(`🚪 私有事件，需要认证`);
      const token = this.extractTokenFromSocket(client);

      if (!token) {
        this.logger.warn(`❌ WebSocket认证失败: 未提供Token`);
        this.logger.warn(`🔍 握手信息调试:`);
        this.logger.warn(`   - auth对象: ${JSON.stringify(client.handshake.auth)}`);
        this.logger.warn(`   - query参数: ${JSON.stringify(client.handshake.query)}`);
        this.logger.warn(`   - authorization头: ${client.handshake.headers?.authorization}`);
        throw new WsException('Authentication required');
      }

      this.logger.debug(`✅ Token提取成功，开始验证...`);

      // 使用统一的AuthService验证Token
      const result = await this.authService.validateToken(token);

      if (!result.valid) {
        this.logger.warn(`❌ WebSocket Token验证失败: ${result.error}`);
        throw new WsException(result.error || 'Invalid token');
      }

      // 将用户信息附加到 socket 对象
      client.data.user = {
        id: result.user.id,
        username: result.user.username,
        email: result.user.email,
        roles: result.user.roles || [],
        permissions: result.user.permissions || [],
        level: result.user.level,
        tokenScope: result.scope,
      };

      // 检查Token作用域
      const requiredScope = this.reflector.get<'account' | 'character'>(
        'TOKEN_SCOPE_KEY',
        context.getHandler()
      );

      if (requiredScope && result.scope !== requiredScope) {
        this.logger.warn(`❌ Token作用域不匹配，期望: ${requiredScope}, 实际: ${result.scope}`);
        throw new WsException(`Token作用域不匹配，期望: ${requiredScope}, 实际: ${result.scope}`);
      }

      // 检查所需权限
      const requiredRoles = this.reflector.getAllAndOverride<string[]>('roles', [
        context.getHandler(),
        context.getClass(),
      ]);

      if (requiredRoles && requiredRoles.length > 0) {
        const userRoles = result.user.roles || [];
        const hasRequiredRole = requiredRoles.some(role => userRoles.includes(role));

        if (!hasRequiredRole) {
          this.logger.warn(`❌ 权限不足，需要角色: ${requiredRoles.join(', ')}, 用户角色: ${userRoles.join(', ')}`);
          throw new WsException('Insufficient permissions');
        }
      }

      this.logger.debug(`✅ WebSocket authentication successful for user: ${result.user.id}`);
      return true;

    } catch (error) {
      this.logger.error(`❌ WebSocket认证过程出错: ${error.message}`);
      if (error instanceof WsException) {
        throw error;
      }
      throw new WsException('Authentication failed');
    }
  }

  /**
   * 从 Socket 连接中提取 JWT 令牌
   */
  private extractTokenFromSocket(client: Socket): string | null {
    this.logger.debug(`🔍 开始提取WebSocket Token...`);

    // 方法1: 从 auth 对象中获取（推荐方式）
    const authToken = client.handshake.auth?.token;
    this.logger.debug(`🔍 Auth对象Token: ${authToken ? '存在' : '不存在'}`);
    if (authToken && typeof authToken === 'string') {
      this.logger.debug(`✅ 从auth对象获取到Token，长度: ${authToken.length}`);
      return authToken;
    }

    // 方法2: 从查询参数中获取
    const queryToken = client.handshake.query?.token;
    this.logger.debug(`🔍 查询参数Token: ${queryToken ? '存在' : '不存在'}`);
    if (queryToken && typeof queryToken === 'string') {
      this.logger.debug(`✅ 从查询参数获取到Token，长度: ${queryToken.length}`);
      return queryToken;
    }

    // 方法3: 从 Authorization 头中获取
    const authHeader = client.handshake.headers?.authorization;
    this.logger.debug(`🔍 Authorization头: ${authHeader ? '存在' : '不存在'}`);
    if (authHeader && typeof authHeader === 'string' && authHeader.startsWith('Bearer ')) {
      const headerToken = authHeader.substring(7);
      this.logger.debug(`✅ 从Authorization头获取到Token，长度: ${headerToken.length}`);
      return headerToken;
    }

    this.logger.warn(`❌ 未能从任何来源提取到Token`);
    return null;
  }
}
