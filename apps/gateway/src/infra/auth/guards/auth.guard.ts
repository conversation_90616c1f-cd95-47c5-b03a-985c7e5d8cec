import { Injectable, CanActivate, ExecutionContext, UnauthorizedException } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { JwtService } from '@nestjs/jwt';
import { AuthService } from '../services/auth.service';
import { CharacterAuthService } from '../services/character-auth.service';

/**
 * 认证守卫
 * 
 * 验证请求的认证状态，确保只有经过认证的用户才能访问受保护的资源
 */
@Injectable()
export class AuthGuard implements CanActivate {
  constructor(
    private readonly authService: AuthService,
    private readonly characterAuthService: CharacterAuthService,
    private readonly jwtService: JwtService,
    private readonly reflector: Reflector,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    // 检查是否需要认证
    const isPublic = this.reflector.getAllAndOverride<boolean>('isPublic', [
      context.getHandler(),
      context.getClass(),
    ]);

    if (isPublic) {
      return true;
    }

    const request = context.switchToHttp().getRequest();
    const token = this.extractTokenFromRequest(request);

    if (!token) {
      throw new UnauthorizedException('Authentication token is required');
    }

    try {
      // 验证双Token：根据Token类型调用对应的服务
      const validationResult = await this.validateDualToken(token);

      // 将用户信息注入到请求对象
      request.user = validationResult.user;
      if (validationResult.character) {
        request.character = validationResult.character;
      }

      // 检查Token作用域
      const requiredScope = this.reflector.get<'account' | 'character'>('tokenScope', context.getHandler());
      if (requiredScope && validationResult.tokenScope !== requiredScope) {
        throw new UnauthorizedException(`Token作用域不匹配，期望: ${requiredScope}, 实际: ${validationResult.tokenScope}`);
      }

      return true;
    } catch (error) {
      throw new UnauthorizedException(error.message || 'Invalid authentication token');
    }
  }

  /**
   * 从请求中提取认证令牌
   */
  private extractTokenFromRequest(request: any): string | null {
    // 从 Authorization 头中提取
    const authHeader = request.headers?.authorization;
    if (authHeader && authHeader.startsWith('Bearer ')) {
      return authHeader.substring(7);
    }

    // 从查询参数中提取
    if (request.query?.token) {
      return request.query.token;
    }

    // 从 Cookie 中提取
    if (request.cookies?.token) {
      return request.cookies.token;
    }

    return null;
  }

  /**
   * 验证双Token：根据Token类型调用对应的服务
   * 支持账号Token和角色Token的验证
   */
  private async validateDualToken(token: string): Promise<{
    user: any;
    character?: any;
    tokenScope: 'account' | 'character';
  }> {
    try {
      // 首先解析Token获取基本信息（不验证签名）
      const decoded = this.jwtService.decode(token) as any;

      if (!decoded || !decoded.scope) {
        throw new UnauthorizedException('Invalid token format');
      }

      // 根据scope字段选择验证服务
      switch (decoded.scope) {
        case 'account':
          return await this.validateAccountTokenViaService(token);
        case 'character':
          return await this.validateCharacterTokenViaService(token);
        default:
          throw new UnauthorizedException(`Unsupported token scope: ${decoded.scope}`);
      }
    } catch (error) {
      if (error instanceof UnauthorizedException) {
        throw error;
      }
      throw new UnauthorizedException('Token validation failed');
    }
  }

  /**
   * 通过AuthService验证账号Token
   */
  private async validateAccountTokenViaService(token: string): Promise<{
    user: any;
    tokenScope: 'account';
  }> {
    const result = await this.authService.validateToken(token);

    if (!result.valid) {
      throw new UnauthorizedException(result.error || '账号Token验证失败');
    }

    // 验证Token作用域
    if (result.tokenScope !== 'account') {
      throw new UnauthorizedException('Expected account token');
    }

    return {
      user: result.user,
      tokenScope: 'account',
    };
  }

  /**
   * 通过CharacterAuthService验证角色Token
   */
  private async validateCharacterTokenViaService(token: string): Promise<{
    user: any;
    character: any;
    tokenScope: 'character';
  }> {
    const result = await this.characterAuthService.validateCharacterToken(token);

    if (!result.valid) {
      throw new UnauthorizedException(result.error || '角色Token验证失败');
    }

    // 验证Token作用域
    if (result.tokenScope !== 'character') {
      throw new UnauthorizedException('Expected character token');
    }

    // 验证角色上下文
    if (!result.character?.characterId || !result.character?.serverId) {
      throw new UnauthorizedException('Character token must contain character and server information');
    }

    return {
      user: result.user,
      character: result.character,
      tokenScope: 'character',
    };
  }
}
