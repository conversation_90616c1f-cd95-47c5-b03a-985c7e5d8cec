import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { RedisModule } from '@common/redis';
import { MicroserviceKitModule } from '@common/microservice-kit';
import { RateLimitModule } from '../modules/rate-limiting/rate-limit.module';

// 服务
import { AuthService } from './services/auth.service';
import { CharacterAuthService } from './services/character-auth.service';
// 注意：UserService已简化，CRUD操作通过HTTP路由转发处理

// 守卫
import { AuthGuard } from './guards/auth.guard';
import { RateLimitGuard } from './guards/rate-limit.guard';
import { WsAuthGuard } from './guards/ws-auth.guard';

/**
 * 认证基础设施模块
 *
 * 提供统一的认证服务和守卫，供Gateway各模块使用
 * 包括：
 * - 认证服务（Token验证、权限检查、缓存管理）
 * - 角色认证服务（角色Token生成和验证）
 * - 认证守卫（HTTP和WebSocket认证）
 * - 限流守卫（防止滥用）
 *
 * 注意：用户CRUD操作（登录、注册、密码修改等）通过HTTP路由转发直接代理到Auth服务
 */
@Module({
  imports: [
    ConfigModule,
    RedisModule,
    MicroserviceKitModule,
    RateLimitModule, // RateLimitGuard需要RateLimitService
  ],
  providers: [
    // 核心认证服务
    AuthService,
    CharacterAuthService,

    // 认证守卫
    AuthGuard,
    RateLimitGuard,
    WsAuthGuard,
  ],
  exports: [
    // 导出服务供其他模块使用
    AuthService,
    CharacterAuthService,

    // 导出守卫供其他模块使用
    AuthGuard,
    RateLimitGuard,
    WsAuthGuard,
  ],
})
export class AuthModule {}
