import { SetMetadata } from '@nestjs/common';

export const TOKEN_SCOPE_KEY = 'tokenScope';

/**
 * Token作用域装饰器
 * 用于标记WebSocket事件需要的Token类型
 */
export const TokenScope = (scope: 'account' | 'character') => SetMetadata(TOKEN_SCOPE_KEY, scope);

/**
 * 账号Token装饰器
 * 标记WebSocket事件只能使用账号级Token访问
 * 
 * @example
 * ```typescript
 * @AccountToken()
 * @SubscribeMessage('auth.getServerList')
 * async getServerList(client: Socket, data: any) {
 *   // 只有账号Token可以访问
 * }
 * ```
 */
export const AccountToken = () => TokenScope('account');

/**
 * 角色Token装饰器
 * 标记WebSocket事件只能使用角色级Token访问
 * 
 * @example
 * ```typescript
 * @CharacterToken()
 * @SubscribeMessage('character.getInfo')
 * async getCharacterInfo(client: Socket, data: any) {
 *   // 只有角色Token可以访问
 *   // 可以从client.data.character获取角色上下文
 * }
 * ```
 */
export const CharacterToken = () => TokenScope('character');
