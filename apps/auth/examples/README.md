# 🎯 足球经理认证服务使用示例

本目录包含了足球经理认证服务的核心功能使用示例，帮助开发者快速上手和集成。

## 📁 示例目录结构

```
examples/
├── README.md                    # 示例说明文档
├── basic-usage/                 # 基础用法示例
│   ├── user-registration.js     # 用户注册示例
│   ├── user-login.js           # 用户登录示例
│   ├── token-management.js     # 令牌管理示例
│   └── password-operations.js  # 密码操作示例
├── mfa-integration/            # 多因子认证集成
│   ├── totp-setup.js          # TOTP设置示例
│   ├── mfa-login.js           # MFA登录示例
│   └── backup-codes.js        # 备用码管理示例
├── role-permissions/           # 角色权限示例
│   ├── role-management.js     # 角色管理示例
│   ├── permission-check.js    # 权限检查示例
│   └── game-roles.js          # 游戏角色示例
├── security-features/          # 安全功能示例
│   ├── audit-logging.js       # 审计日志示例
│   ├── risk-assessment.js     # 风险评估示例
│   └── session-management.js  # 会话管理示例
├── client-integration/         # 客户端集成示例
│   ├── web-client.html        # Web客户端示例
│   ├── mobile-client.js       # 移动端集成示例
│   └── game-client.js         # 游戏客户端示例
├── advanced-usage/             # 高级用法示例
│   ├── custom-guards.js       # 自定义守卫示例
│   ├── middleware-usage.js    # 中间件使用示例
│   └── event-handling.js      # 事件处理示例
└── deployment/                 # 部署示例
    ├── docker-compose.yml     # Docker部署示例
    ├── kubernetes.yaml        # K8s部署示例
    └── nginx.conf             # Nginx配置示例
```

## 🚀 快速开始

### 1. 环境准备

```bash
# 安装依赖
npm install axios

# 设置环境变量
export AUTH_SERVICE_URL=http://localhost:3001
export API_KEY=your-api-key
```

### 2. 基础认证流程

```javascript
// 完整的认证流程示例
const authClient = require('./basic-usage/auth-client');

async function quickStart() {
  try {
    // 1. 用户注册
    const user = await authClient.register({
      username: 'player001',
      email: '<EMAIL>',
      password: 'SecurePassword123!',
      profile: {
        firstName: '张',
        lastName: '三',
        language: 'zh'
      }
    });

    // 2. 用户登录
    const loginResult = await authClient.login({
      identifier: 'player001',
      password: 'SecurePassword123!'
    });

    // 3. 使用令牌访问受保护资源
    const profile = await authClient.getProfile(loginResult.tokens.accessToken);

    console.log('认证流程完成！', { user, profile });
  } catch (error) {
    console.error('认证失败:', error.message);
  }
}

quickStart();
```

## 📚 示例分类说明

### 🔰 基础用法 (basic-usage/)
- **用户注册**: 完整的用户注册流程，包括验证和错误处理
- **用户登录**: 多种登录方式（用户名/邮箱）和设备管理
- **令牌管理**: JWT令牌的获取、刷新、验证和撤销
- **密码操作**: 密码修改、重置和安全策略

### 🔐 多因子认证 (mfa-integration/)
- **TOTP设置**: 生成密钥、QR码和启用流程
- **MFA登录**: 带有多因子认证的登录流程
- **备用码**: 备用码生成、使用和管理

### 👥 角色权限 (role-permissions/)
- **角色管理**: 创建、分配和管理用户角色
- **权限检查**: 在应用中检查用户权限
- **游戏角色**: 足球经理游戏专用角色使用

### 🛡️ 安全功能 (security-features/)
- **审计日志**: 记录和查询用户操作日志
- **风险评估**: 实时风险评分和安全建议
- **会话管理**: 多设备会话控制和管理

### 💻 客户端集成 (client-integration/)
- **Web客户端**: 浏览器端集成示例
- **移动端**: React Native/Flutter集成
- **游戏客户端**: Unity/Unreal Engine集成

### 🔧 高级用法 (advanced-usage/)
- **自定义守卫**: 创建自定义权限守卫
- **中间件**: 认证中间件的使用
- **事件处理**: 安全事件的监听和处理

### 🚀 部署示例 (deployment/)
- **Docker**: 容器化部署配置
- **Kubernetes**: 集群部署示例
- **Nginx**: 反向代理和负载均衡

## 🎮 游戏特色示例

### 球队经理角色示例
```javascript
// 为用户分配球队经理角色
await authClient.assignRole(userId, 'team_manager');

// 检查球员交易权限
const canTrade = await authClient.checkPermission(userId, 'player:trade');

// 创建球队
if (canTrade) {
  await gameClient.createTeam(teamData);
}
```

### 联赛管理示例
```javascript
// 联赛管理员权限检查
const isLeagueAdmin = await authClient.hasRole(userId, 'league_admin');

if (isLeagueAdmin) {
  // 创建新赛季
  await gameClient.createSeason(seasonData);
  
  // 安排比赛
  await gameClient.scheduleMatches(matchData);
}
```

## 🔍 错误处理示例

```javascript
try {
  await authClient.login(credentials);
} catch (error) {
  switch (error.code) {
    case 'INVALID_CREDENTIALS':
      console.log('用户名或密码错误');
      break;
    case 'ACCOUNT_LOCKED':
      console.log('账户已被锁定');
      break;
    case 'MFA_REQUIRED':
      console.log('需要多因子认证');
      break;
    case 'RATE_LIMITED':
      console.log('请求过于频繁，请稍后重试');
      break;
    default:
      console.log('登录失败:', error.message);
  }
}
```

## 📊 性能优化示例

```javascript
// 令牌缓存
const tokenCache = new Map();

// 批量权限检查
const permissions = await authClient.checkMultiplePermissions(userId, [
  'team:manage',
  'player:trade',
  'finance:view'
]);

// 会话复用
const session = await authClient.getOrCreateSession(deviceInfo);
```

## 🧪 测试示例

```javascript
// 单元测试示例
describe('认证服务', () => {
  it('应该成功注册新用户', async () => {
    const result = await authClient.register(validUserData);
    expect(result.success).toBe(true);
    expect(result.user.id).toBeDefined();
  });

  it('应该拒绝弱密码', async () => {
    await expect(
      authClient.register({ ...validUserData, password: '123' })
    ).rejects.toThrow('密码强度不足');
  });
});
```

## 📞 获取帮助

如果您在使用示例时遇到问题：

1. 查看 [API文档](../docs/API.md)
2. 阅读 [故障排除指南](../docs/TROUBLESHOOTING.md)
3. 提交 [Issue](https://github.com/your-repo/issues)
4. 联系技术支持: <EMAIL>

---

**开始探索示例，构建您的足球经理游戏认证系统！** ⚽🏆
