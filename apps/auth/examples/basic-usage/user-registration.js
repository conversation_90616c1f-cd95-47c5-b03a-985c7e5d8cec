/**
 * 用户注册示例
 * 
 * 演示如何使用认证服务进行用户注册，包括：
 * - 基础用户注册
 * - 游戏玩家注册
 * - 球队经理注册
 * - 错误处理和验证
 */

const authClient = require('./auth-client');

/**
 * 基础用户注册示例
 */
async function basicUserRegistration() {
  console.log('\n🔰 基础用户注册示例');
  console.log('='.repeat(40));

  try {
    const userData = {
      username: 'newplayer001',
      email: '<EMAIL>',
      password: 'SecurePassword123!',
      profile: {
        firstName: '张',
        lastName: '三',
        language: 'zh',
      },
      acceptTerms: true,
    };

    const result = await authClient.register(userData);
    
    console.log('✅ 注册成功!');
    console.log('用户ID:', result.data.user.id);
    console.log('用户名:', result.data.user.username);
    console.log('邮箱:', result.data.user.email);
    console.log('创建时间:', result.data.user.createdAt);
    
    return result.data.user;
  } catch (error) {
    console.error('❌ 注册失败:', error.message);
    if (error.details) {
      console.error('详细错误:', error.details);
    }
    throw error;
  }
}

/**
 * 游戏玩家注册示例
 */
async function gamePlayerRegistration() {
  console.log('\n🎮 游戏玩家注册示例');
  console.log('='.repeat(40));

  try {
    const playerData = {
      username: 'footballer2024',
      email: '<EMAIL>',
      password: 'MyFootballPass123!',
      profile: {
        firstName: '李',
        lastName: '四',
        language: 'zh',
        bio: '热爱足球的游戏玩家',
      },
      gameProfile: {
        preferredPosition: 'midfielder',
        favoriteTeam: 'Barcelona',
        experienceLevel: 'intermediate',
      },
      acceptTerms: true,
    };

    const result = await authClient.register(playerData);
    
    console.log('✅ 游戏玩家注册成功!');
    console.log('用户信息:', {
      id: result.data.user.id,
      username: result.data.user.username,
      gameProfile: result.data.user.gameProfile,
    });
    
    return result.data.user;
  } catch (error) {
    console.error('❌ 游戏玩家注册失败:', error.message);
    throw error;
  }
}

/**
 * 球队经理注册示例
 */
async function teamManagerRegistration() {
  console.log('\n⚽ 球队经理注册示例');
  console.log('='.repeat(40));

  try {
    const managerData = {
      username: 'coach_master',
      email: '<EMAIL>',
      password: 'CoachingExpert123!',
      profile: {
        firstName: '王',
        lastName: '教练',
        language: 'zh',
        bio: '资深足球教练，专注青训',
      },
      gameProfile: {
        preferredPosition: 'coach',
        favoriteTeam: 'Real Madrid',
        experienceLevel: 'expert',
        coachingLicense: 'UEFA Pro',
        specialization: 'youth_development',
      },
      acceptTerms: true,
    };

    const result = await authClient.register(managerData);
    
    console.log('✅ 球队经理注册成功!');
    console.log('经理信息:', {
      id: result.data.user.id,
      username: result.data.user.username,
      specialization: result.data.user.gameProfile?.specialization,
    });
    
    return result.data.user;
  } catch (error) {
    console.error('❌ 球队经理注册失败:', error.message);
    throw error;
  }
}

/**
 * 批量用户注册示例
 */
async function batchUserRegistration() {
  console.log('\n📦 批量用户注册示例');
  console.log('='.repeat(40));

  const users = [
    {
      username: 'player001',
      email: '<EMAIL>',
      password: 'Player123!',
      profile: { firstName: '球员', lastName: '一号', language: 'zh' },
      gameProfile: { experienceLevel: 'beginner' },
    },
    {
      username: 'player002',
      email: '<EMAIL>',
      password: 'Player123!',
      profile: { firstName: '球员', lastName: '二号', language: 'zh' },
      gameProfile: { experienceLevel: 'intermediate' },
    },
    {
      username: 'player003',
      email: '<EMAIL>',
      password: 'Player123!',
      profile: { firstName: '球员', lastName: '三号', language: 'zh' },
      gameProfile: { experienceLevel: 'expert' },
    },
  ];

  const results = [];
  const errors = [];

  for (const userData of users) {
    try {
      userData.acceptTerms = true;
      const result = await authClient.register(userData);
      results.push(result.data.user);
      console.log(`✅ ${userData.username} 注册成功`);
    } catch (error) {
      errors.push({ username: userData.username, error: error.message });
      console.log(`❌ ${userData.username} 注册失败: ${error.message}`);
    }
  }

  console.log(`\n📊 批量注册结果:`);
  console.log(`成功: ${results.length} 个用户`);
  console.log(`失败: ${errors.length} 个用户`);

  return { success: results, errors };
}

/**
 * 注册验证示例
 */
async function registrationValidationExamples() {
  console.log('\n🔍 注册验证示例');
  console.log('='.repeat(40));

  const invalidCases = [
    {
      name: '弱密码',
      data: {
        username: 'weakpass',
        email: '<EMAIL>',
        password: '123',
        profile: { firstName: '测试', lastName: '用户', language: 'zh' },
      },
    },
    {
      name: '无效邮箱',
      data: {
        username: 'invalidemail',
        email: 'not-an-email',
        password: 'ValidPassword123!',
        profile: { firstName: '测试', lastName: '用户', language: 'zh' },
      },
    },
    {
      name: '用户名过短',
      data: {
        username: 'ab',
        email: '<EMAIL>',
        password: 'ValidPassword123!',
        profile: { firstName: '测试', lastName: '用户', language: 'zh' },
      },
    },
    {
      name: '缺少必填字段',
      data: {
        username: 'missingfields',
        // 缺少 email 和 password
        profile: { firstName: '测试', lastName: '用户', language: 'zh' },
      },
    },
  ];

  for (const testCase of invalidCases) {
    try {
      testCase.data.acceptTerms = true;
      await authClient.register(testCase.data);
      console.log(`❌ ${testCase.name}: 应该失败但成功了`);
    } catch (error) {
      console.log(`✅ ${testCase.name}: 正确拒绝 - ${error.message}`);
    }
  }
}

/**
 * 重复注册检测示例
 */
async function duplicateRegistrationTest() {
  console.log('\n🔄 重复注册检测示例');
  console.log('='.repeat(40));

  const userData = {
    username: 'duplicate_test',
    email: '<EMAIL>',
    password: 'DuplicateTest123!',
    profile: {
      firstName: '重复',
      lastName: '测试',
      language: 'zh',
    },
    acceptTerms: true,
  };

  try {
    // 第一次注册
    console.log('第一次注册...');
    const firstResult = await authClient.register(userData);
    console.log('✅ 第一次注册成功:', firstResult.data.user.username);

    // 第二次注册（应该失败）
    console.log('第二次注册（相同用户名）...');
    await authClient.register(userData);
    console.log('❌ 第二次注册不应该成功');
  } catch (error) {
    console.log('✅ 正确检测到重复注册:', error.message);
  }

  try {
    // 相同邮箱，不同用户名
    console.log('相同邮箱，不同用户名...');
    await authClient.register({
      ...userData,
      username: 'different_username',
    });
    console.log('❌ 相同邮箱注册不应该成功');
  } catch (error) {
    console.log('✅ 正确检测到邮箱重复:', error.message);
  }
}

/**
 * 运行所有注册示例
 */
async function runAllRegistrationExamples() {
  console.log('🚀 开始运行用户注册示例...\n');

  try {
    // 基础注册
    await basicUserRegistration();
    
    // 游戏玩家注册
    await gamePlayerRegistration();
    
    // 球队经理注册
    await teamManagerRegistration();
    
    // 批量注册
    await batchUserRegistration();
    
    // 验证示例
    await registrationValidationExamples();
    
    // 重复注册测试
    await duplicateRegistrationTest();
    
    console.log('\n🎉 所有注册示例运行完成!');
  } catch (error) {
    console.error('\n💥 示例运行出错:', error.message);
  }
}

// 如果直接运行此文件
if (require.main === module) {
  runAllRegistrationExamples();
}

module.exports = {
  basicUserRegistration,
  gamePlayerRegistration,
  teamManagerRegistration,
  batchUserRegistration,
  registrationValidationExamples,
  duplicateRegistrationTest,
  runAllRegistrationExamples,
};
