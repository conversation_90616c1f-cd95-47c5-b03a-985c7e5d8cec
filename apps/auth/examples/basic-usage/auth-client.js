/**
 * 认证客户端 - 封装所有认证相关的API调用
 * 
 * 使用方法:
 * const authClient = require('./auth-client');
 * const result = await authClient.login({ identifier: 'user', password: 'pass' });
 */

const axios = require('axios');

class AuthClient {
  constructor(baseURL = 'http://localhost:3001') {
    this.baseURL = baseURL;
    this.accessToken = null;
    this.refreshToken = null;
    
    // 创建axios实例
    this.api = axios.create({
      baseURL: this.baseURL,
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // 请求拦截器 - 自动添加认证头
    this.api.interceptors.request.use((config) => {
      if (this.accessToken) {
        config.headers.Authorization = `Bearer ${this.accessToken}`;
      }
      return config;
    });

    // 响应拦截器 - 自动处理令牌刷新
    this.api.interceptors.response.use(
      (response) => response,
      async (error) => {
        const originalRequest = error.config;
        
        if (error.response?.status === 401 && !originalRequest._retry) {
          originalRequest._retry = true;
          
          try {
            await this.refreshAccessToken();
            originalRequest.headers.Authorization = `Bearer ${this.accessToken}`;
            return this.api(originalRequest);
          } catch (refreshError) {
            // 刷新失败，清除令牌
            this.clearTokens();
            throw refreshError;
          }
        }
        
        throw error;
      }
    );
  }

  /**
   * 用户注册
   */
  async register(userData) {
    try {
      const response = await this.api.post('/auth/register', {
        username: userData.username,
        email: userData.email,
        password: userData.password,
        confirmPassword: userData.confirmPassword || userData.password,
        profile: {
          firstName: userData.profile?.firstName || '',
          lastName: userData.profile?.lastName || '',
          language: userData.profile?.language || 'zh',
          avatar: userData.profile?.avatar || null,
          bio: userData.profile?.bio || null,
        },
        gameProfile: {
          preferredPosition: userData.gameProfile?.preferredPosition || null,
          favoriteTeam: userData.gameProfile?.favoriteTeam || null,
          experienceLevel: userData.gameProfile?.experienceLevel || 'beginner',
        },
        acceptTerms: userData.acceptTerms !== false, // 默认为true
      });

      console.log('✅ 用户注册成功:', response.data.data.user.username);
      return response.data;
    } catch (error) {
      console.error('❌ 用户注册失败:', error.response?.data?.message || error.message);
      throw this.handleError(error);
    }
  }

  /**
   * 用户登录
   */
  async login(credentials, deviceInfo = null) {
    try {
      const response = await this.api.post('/auth/login', {
        identifier: credentials.identifier,
        password: credentials.password,
        mfaCode: credentials.mfaCode || null,
        rememberMe: credentials.rememberMe || false,
        deviceInfo: deviceInfo || {
          type: 'web',
          name: 'Example Client',
          fingerprint: `example-${Date.now()}`,
          userAgent: 'Example/1.0',
          ipAddress: '127.0.0.1',
        },
      });

      // 保存令牌
      this.accessToken = response.data.data.tokens.accessToken;
      this.refreshToken = response.data.data.tokens.refreshToken;

      console.log('✅ 用户登录成功:', response.data.data.user.username);
      return response.data;
    } catch (error) {
      console.error('❌ 用户登录失败:', error.response?.data?.message || error.message);
      throw this.handleError(error);
    }
  }

  /**
   * 刷新访问令牌
   */
  async refreshAccessToken() {
    if (!this.refreshToken) {
      throw new Error('没有可用的刷新令牌');
    }

    try {
      const response = await this.api.post('/auth/refresh', {
        refreshToken: this.refreshToken,
      });

      this.accessToken = response.data.data.tokens.accessToken;
      this.refreshToken = response.data.data.tokens.refreshToken;

      console.log('✅ 令牌刷新成功');
      return response.data;
    } catch (error) {
      console.error('❌ 令牌刷新失败:', error.response?.data?.message || error.message);
      throw this.handleError(error);
    }
  }

  /**
   * 验证令牌
   */
  async verifyToken() {
    try {
      const response = await this.api.post('/auth/verify-token');
      console.log('✅ 令牌验证成功');
      return response.data;
    } catch (error) {
      console.error('❌ 令牌验证失败:', error.response?.data?.message || error.message);
      throw this.handleError(error);
    }
  }

  /**
   * 用户登出
   */
  async logout(allDevices = false) {
    try {
      const response = await this.api.post('/auth/logout', {
        refreshToken: this.refreshToken,
        allDevices,
      });

      this.clearTokens();
      console.log('✅ 用户登出成功');
      return response.data;
    } catch (error) {
      console.error('❌ 用户登出失败:', error.response?.data?.message || error.message);
      this.clearTokens(); // 即使失败也清除本地令牌
      throw this.handleError(error);
    }
  }

  /**
   * 获取当前用户信息
   */
  async getProfile() {
    try {
      const response = await this.api.get('/users/me');
      console.log('✅ 获取用户信息成功');
      return response.data;
    } catch (error) {
      console.error('❌ 获取用户信息失败:', error.response?.data?.message || error.message);
      throw this.handleError(error);
    }
  }

  /**
   * 更新用户信息
   */
  async updateProfile(profileData) {
    try {
      const response = await this.api.put('/users/me', profileData);
      console.log('✅ 用户信息更新成功');
      return response.data;
    } catch (error) {
      console.error('❌ 用户信息更新失败:', error.response?.data?.message || error.message);
      throw this.handleError(error);
    }
  }

  /**
   * 修改密码
   */
  async changePassword(passwordData) {
    try {
      const response = await this.api.put('/users/me/password', {
        currentPassword: passwordData.currentPassword,
        newPassword: passwordData.newPassword,
        confirmPassword: passwordData.confirmPassword || passwordData.newPassword,
      });

      console.log('✅ 密码修改成功');
      return response.data;
    } catch (error) {
      console.error('❌ 密码修改失败:', error.response?.data?.message || error.message);
      throw this.handleError(error);
    }
  }

  /**
   * 检查用户权限
   */
  async checkPermission(resource, action) {
    try {
      const response = await this.api.post('/auth/check-permission', {
        resource,
        action,
      });

      return response.data.data.hasPermission;
    } catch (error) {
      console.error('❌ 权限检查失败:', error.response?.data?.message || error.message);
      return false;
    }
  }

  /**
   * 检查用户角色
   */
  async hasRole(roleName) {
    try {
      const profile = await this.getProfile();
      return profile.data.user.roles.includes(roleName);
    } catch (error) {
      console.error('❌ 角色检查失败:', error.message);
      return false;
    }
  }

  /**
   * 获取用户会话列表
   */
  async getSessions() {
    try {
      const response = await this.api.get('/users/me/sessions');
      console.log('✅ 获取会话列表成功');
      return response.data;
    } catch (error) {
      console.error('❌ 获取会话列表失败:', error.response?.data?.message || error.message);
      throw this.handleError(error);
    }
  }

  /**
   * 终止指定会话
   */
  async terminateSession(sessionId) {
    try {
      const response = await this.api.delete(`/users/me/sessions/${sessionId}`);
      console.log('✅ 会话终止成功');
      return response.data;
    } catch (error) {
      console.error('❌ 会话终止失败:', error.response?.data?.message || error.message);
      throw this.handleError(error);
    }
  }

  /**
   * 清除本地令牌
   */
  clearTokens() {
    this.accessToken = null;
    this.refreshToken = null;
  }

  /**
   * 检查是否已登录
   */
  isAuthenticated() {
    return !!this.accessToken;
  }

  /**
   * 错误处理
   */
  handleError(error) {
    if (error.response) {
      // 服务器响应错误
      const { status, data } = error.response;
      const message = data?.message || '请求失败';
      const errorCode = data?.errorCode || 'UNKNOWN_ERROR';
      
      const customError = new Error(message);
      customError.code = errorCode;
      customError.status = status;
      customError.details = data?.errors || null;
      
      return customError;
    } else if (error.request) {
      // 网络错误
      const networkError = new Error('网络连接失败，请检查网络设置');
      networkError.code = 'NETWORK_ERROR';
      return networkError;
    } else {
      // 其他错误
      return error;
    }
  }
}

// 创建默认实例
const authClient = new AuthClient();

module.exports = authClient;
module.exports.AuthClient = AuthClient;
