/**
 * 足球经理游戏角色示例
 * 
 * 演示足球经理游戏中的角色系统，包括：
 * - 游戏角色分配
 * - 权限检查
 * - 角色升级
 * - 游戏场景应用
 */

const authClient = require('../basic-usage/auth-client');

/**
 * 角色权限客户端
 */
class RolePermissionClient {
  constructor() {
    this.authClient = authClient;
  }

  /**
   * 获取所有角色
   */
  async getAllRoles() {
    try {
      const response = await this.authClient.api.get('/roles');
      return response.data;
    } catch (error) {
      console.error('❌ 获取角色列表失败:', error.response?.data?.message || error.message);
      throw error;
    }
  }

  /**
   * 获取游戏角色
   */
  async getGameRoles() {
    try {
      const response = await this.authClient.api.get('/roles');
      const gameRoles = response.data.data.filter(role => 
        ['team_management', 'game', 'scouting', 'youth_development', 'finance_management', 'community', 'media', 'player', 'premium'].includes(role.category)
      );
      return { ...response.data, data: gameRoles };
    } catch (error) {
      console.error('❌ 获取游戏角色失败:', error.response?.data?.message || error.message);
      throw error;
    }
  }

  /**
   * 为用户分配角色
   */
  async assignRole(userId, roleName) {
    try {
      const response = await this.authClient.api.post(`/users/${userId}/roles`, {
        roles: [roleName],
      });
      return response.data;
    } catch (error) {
      console.error('❌ 分配角色失败:', error.response?.data?.message || error.message);
      throw error;
    }
  }

  /**
   * 检查权限
   */
  async checkPermission(resource, action) {
    try {
      const response = await this.authClient.api.post('/auth/check-permission', {
        resource,
        action,
      });
      return response.data.data.hasPermission;
    } catch (error) {
      console.error('❌ 权限检查失败:', error.response?.data?.message || error.message);
      return false;
    }
  }

  /**
   * 批量检查权限
   */
  async checkMultiplePermissions(permissions) {
    const results = {};
    
    for (const { resource, action } of permissions) {
      const key = `${resource}:${action}`;
      results[key] = await this.checkPermission(resource, action);
    }
    
    return results;
  }
}

/**
 * 游戏角色展示
 */
async function displayGameRoles() {
  console.log('\n🎮 足球经理游戏角色系统');
  console.log('='.repeat(50));

  const roleClient = new RolePermissionClient();

  try {
    const roles = await roleClient.getGameRoles();
    
    console.log(`📋 共有 ${roles.data.length} 个游戏角色:\n`);

    // 按分类组织角色
    const rolesByCategory = roles.data.reduce((acc, role) => {
      if (!acc[role.category]) {
        acc[role.category] = [];
      }
      acc[role.category].push(role);
      return acc;
    }, {});

    // 分类显示
    const categoryNames = {
      team_management: '🏟️ 球队管理',
      scouting: '🔍 球探系统',
      youth_development: '👨‍🏫 青训发展',
      finance_management: '💰 财务管理',
      community: '👥 社区管理',
      media: '📰 媒体报道',
      player: '⚽ 普通玩家',
      premium: '⭐ 高级会员',
      game: '🎯 游戏核心',
    };

    Object.entries(rolesByCategory).forEach(([category, categoryRoles]) => {
      console.log(`${categoryNames[category] || category}:`);
      
      categoryRoles.forEach(role => {
        console.log(`  • ${role.displayName} (${role.name})`);
        console.log(`    ${role.description}`);
        console.log(`    优先级: ${role.priority} | 颜色: ${role.color || '默认'}`);
        console.log('');
      });
    });

    return roles.data;
  } catch (error) {
    console.error('❌ 显示游戏角色失败:', error.message);
    throw error;
  }
}

/**
 * 球队经理角色示例
 */
async function teamManagerRoleExample() {
  console.log('\n⚽ 球队经理角色示例');
  console.log('='.repeat(40));

  const roleClient = new RolePermissionClient();

  try {
    // 获取当前用户信息
    const profile = await authClient.getProfile();
    const userId = profile.data.user.id;

    console.log(`👤 当前用户: ${profile.data.user.username}`);

    // 检查球队经理权限
    console.log('\n🔍 检查球队经理权限...');
    
    const managerPermissions = [
      { resource: 'team', action: 'create' },
      { resource: 'team', action: 'manage' },
      { resource: 'player', action: 'scout' },
      { resource: 'player', action: 'sign' },
      { resource: 'player', action: 'trade' },
      { resource: 'match', action: 'lineup' },
      { resource: 'match', action: 'tactics' },
      { resource: 'transfer', action: 'bid' },
      { resource: 'finance', action: 'budget' },
      { resource: 'facility', action: 'upgrade' },
    ];

    const permissionResults = await roleClient.checkMultiplePermissions(managerPermissions);

    console.log('📊 球队经理权限检查结果:');
    Object.entries(permissionResults).forEach(([permission, hasPermission]) => {
      const status = hasPermission ? '✅' : '❌';
      console.log(`  ${status} ${permission}`);
    });

    // 模拟球队管理操作
    console.log('\n🏟️ 模拟球队管理操作:');

    if (permissionResults['team:create']) {
      console.log('✅ 可以创建球队');
      console.log('  → 创建新球队 "梦想FC"');
    } else {
      console.log('❌ 无法创建球队，需要球队经理权限');
    }

    if (permissionResults['player:scout']) {
      console.log('✅ 可以球探球员');
      console.log('  → 搜索年轻有潜力的球员');
    }

    if (permissionResults['transfer:bid']) {
      console.log('✅ 可以参与转会');
      console.log('  → 为目标球员出价');
    }

    if (permissionResults['finance:budget']) {
      console.log('✅ 可以管理预算');
      console.log('  → 制定赛季预算计划');
    }

    return permissionResults;
  } catch (error) {
    console.error('❌ 球队经理角色示例失败:', error.message);
    throw error;
  }
}

/**
 * 角色升级示例
 */
async function roleUpgradeExample() {
  console.log('\n📈 角色升级示例');
  console.log('='.repeat(40));

  const roleClient = new RolePermissionClient();

  try {
    const profile = await authClient.getProfile();
    const userId = profile.data.user.id;
    const currentRoles = profile.data.user.roles || [];

    console.log(`👤 用户: ${profile.data.user.username}`);
    console.log(`📋 当前角色: ${currentRoles.join(', ') || '无'}`);

    // 角色升级路径
    const upgradePathes = {
      'trial_user': 'casual_player',
      'casual_player': 'team_manager',
      'team_manager': 'vip_member',
      'scout': 'assistant_coach',
      'assistant_coach': 'team_manager',
      'youth_coach': 'assistant_coach',
    };

    console.log('\n🎯 可能的升级路径:');
    
    for (const currentRole of currentRoles) {
      const nextRole = upgradePathes[currentRole];
      if (nextRole) {
        console.log(`  ${currentRole} → ${nextRole}`);
        
        // 模拟升级条件检查
        const canUpgrade = await checkUpgradeConditions(currentRole, nextRole);
        if (canUpgrade) {
          console.log(`    ✅ 满足升级条件`);
        } else {
          console.log(`    ❌ 不满足升级条件`);
        }
      }
    }

    // 模拟VIP升级
    if (currentRoles.includes('casual_player')) {
      console.log('\n⭐ VIP会员升级示例:');
      console.log('  检查VIP升级条件...');
      
      const vipConditions = {
        gameTime: 100, // 游戏时长（小时）
        achievements: 5, // 成就数量
        teamValue: 1000000, // 球队价值
        payment: true, // 付费状态
      };

      console.log('  📊 VIP升级条件:');
      console.log(`    游戏时长: ${vipConditions.gameTime}小时 ✅`);
      console.log(`    成就数量: ${vipConditions.achievements}个 ✅`);
      console.log(`    球队价值: $${vipConditions.teamValue.toLocaleString()} ✅`);
      console.log(`    付费状态: ${vipConditions.payment ? '已付费' : '未付费'} ✅`);
      
      if (Object.values(vipConditions).every(condition => condition)) {
        console.log('  🎉 恭喜！您符合VIP会员升级条件');
        console.log('  💎 升级后将获得以下特权:');
        console.log('    • 高级球探功能');
        console.log('    • 无限制转会次数');
        console.log('    • 专属VIP联赛');
        console.log('    • 优先客服支持');
      }
    }

    return upgradePathes;
  } catch (error) {
    console.error('❌ 角色升级示例失败:', error.message);
    throw error;
  }
}

/**
 * 检查升级条件
 */
async function checkUpgradeConditions(currentRole, nextRole) {
  // 模拟升级条件检查
  const conditions = {
    'trial_user_to_casual_player': {
      gameTime: 10, // 10小时游戏时间
      tutorialCompleted: true,
    },
    'casual_player_to_team_manager': {
      gameTime: 50,
      teamsCreated: 1,
      matchesPlayed: 10,
    },
    'team_manager_to_vip_member': {
      gameTime: 100,
      achievements: 5,
      payment: true,
    },
  };

  const conditionKey = `${currentRole}_to_${nextRole}`;
  const condition = conditions[conditionKey];

  if (!condition) {
    return Math.random() > 0.5; // 随机结果
  }

  // 模拟条件检查
  return Object.values(condition).every(c => c === true || (typeof c === 'number' && c > 0));
}

/**
 * 游戏场景权限应用
 */
async function gameScenarioPermissions() {
  console.log('\n🎯 游戏场景权限应用');
  console.log('='.repeat(40));

  const roleClient = new RolePermissionClient();

  const scenarios = [
    {
      name: '创建球队',
      description: '新玩家想要创建自己的球队',
      requiredPermissions: [{ resource: 'team', action: 'create' }],
    },
    {
      name: '球员交易',
      description: '经理想要买卖球员',
      requiredPermissions: [
        { resource: 'player', action: 'trade' },
        { resource: 'transfer', action: 'bid' },
        { resource: 'finance', action: 'budget' },
      ],
    },
    {
      name: '联赛管理',
      description: '管理员创建新的联赛',
      requiredPermissions: [
        { resource: 'league', action: 'create' },
        { resource: 'league', action: 'manage' },
        { resource: 'match', action: 'schedule' },
      ],
    },
    {
      name: '青训发展',
      description: '教练培养年轻球员',
      requiredPermissions: [
        { resource: 'player', action: 'train' },
        { resource: 'facility', action: 'upgrade' },
      ],
    },
    {
      name: '媒体报道',
      description: '记者撰写比赛报道',
      requiredPermissions: [
        { resource: 'content', action: 'create' },
        { resource: 'stats', action: 'view' },
        { resource: 'match', action: 'watch' },
      ],
    },
  ];

  console.log('🎮 游戏场景权限检查:\n');

  for (const scenario of scenarios) {
    console.log(`📝 场景: ${scenario.name}`);
    console.log(`   描述: ${scenario.description}`);
    console.log('   权限检查:');

    let canPerformAction = true;
    
    for (const permission of scenario.requiredPermissions) {
      const hasPermission = await roleClient.checkPermission(permission.resource, permission.action);
      const status = hasPermission ? '✅' : '❌';
      console.log(`     ${status} ${permission.resource}:${permission.action}`);
      
      if (!hasPermission) {
        canPerformAction = false;
      }
    }

    console.log(`   结果: ${canPerformAction ? '✅ 可以执行' : '❌ 权限不足'}`);
    
    if (!canPerformAction) {
      console.log('   建议: 联系管理员分配相应角色');
    }
    
    console.log('');
  }
}

/**
 * 角色权限矩阵展示
 */
async function displayRolePermissionMatrix() {
  console.log('\n📊 角色权限矩阵');
  console.log('='.repeat(60));

  const gameRoles = [
    'casual_player',
    'team_manager',
    'scout',
    'youth_coach',
    'financial_director',
    'league_admin',
    'vip_member',
  ];

  const keyPermissions = [
    'team:create',
    'player:scout',
    'player:trade',
    'match:lineup',
    'finance:budget',
    'league:create',
    'content:create',
  ];

  console.log('角色权限对照表:');
  console.log('');
  
  // 表头
  const header = '角色'.padEnd(20) + keyPermissions.map(p => p.split(':')[1].padEnd(8)).join('');
  console.log(header);
  console.log('-'.repeat(header.length));

  // 模拟权限矩阵（实际应该从API获取）
  const permissionMatrix = {
    'casual_player': ['team:create'],
    'team_manager': ['team:create', 'player:scout', 'player:trade', 'match:lineup', 'finance:budget'],
    'scout': ['player:scout'],
    'youth_coach': ['player:scout'],
    'financial_director': ['finance:budget'],
    'league_admin': ['league:create', 'match:lineup'],
    'vip_member': ['team:create', 'player:scout', 'player:trade', 'match:lineup', 'finance:budget', 'content:create'],
  };

  gameRoles.forEach(role => {
    const rolePermissions = permissionMatrix[role] || [];
    let row = role.padEnd(20);
    
    keyPermissions.forEach(permission => {
      const hasPermission = rolePermissions.includes(permission);
      row += (hasPermission ? '✅' : '❌').padEnd(8);
    });
    
    console.log(row);
  });

  console.log('\n📝 说明:');
  console.log('  ✅ = 拥有权限');
  console.log('  ❌ = 无权限');
  console.log('  实际权限可能因角色继承而有所不同');
}

/**
 * 运行所有游戏角色示例
 */
async function runAllGameRoleExamples() {
  console.log('🚀 开始运行游戏角色示例...\n');

  try {
    // 确保已登录
    if (!authClient.isAuthenticated()) {
      await authClient.login({
        identifier: 'admin',
        password: 'Admin123!@#',
      });
      console.log('✅ 已登录\n');
    }

    // 显示游戏角色
    await displayGameRoles();
    
    // 球队经理角色示例
    await teamManagerRoleExample();
    
    // 角色升级示例
    await roleUpgradeExample();
    
    // 游戏场景权限
    await gameScenarioPermissions();
    
    // 权限矩阵
    await displayRolePermissionMatrix();
    
    console.log('\n🎉 所有游戏角色示例运行完成!');
  } catch (error) {
    console.error('\n💥 示例运行出错:', error.message);
  }
}

// 如果直接运行此文件
if (require.main === module) {
  runAllGameRoleExamples();
}

module.exports = {
  RolePermissionClient,
  displayGameRoles,
  teamManagerRoleExample,
  roleUpgradeExample,
  gameScenarioPermissions,
  displayRolePermissionMatrix,
  runAllGameRoleExamples,
};
