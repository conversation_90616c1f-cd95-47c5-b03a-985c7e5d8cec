version: '3.8'

# 生产环境 Docker Compose 配置
# 包含完整的监控、日志、备份和安全配置

services:
  # 认证服务 - 主服务
  auth-service:
    build:
      context: ../../
      dockerfile: Dockerfile
      target: production
    image: football-manager/auth-service:latest
    container_name: fm-auth-service
    restart: unless-stopped
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=production
      - DATABASE_URI=mongodb://mongodb:27017/football_manager_auth
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=${REDIS_PASSWORD}
      - JWT_SECRET=${JWT_SECRET}
      - JWT_REFRESH_SECRET=${JWT_REFRESH_SECRET}
      - ENCRYPTION_KEY=${ENCRYPTION_KEY}
      - LOG_LEVEL=info
      - HEALTH_CHECK_ENABLED=true
    depends_on:
      mongodb:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - football-manager-network
    volumes:
      - auth-logs:/app/logs
      - auth-uploads:/app/uploads
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 512M
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.auth.rule=Host(`auth.footballmanager.com`)"
      - "traefik.http.routers.auth.tls=true"
      - "traefik.http.routers.auth.tls.certresolver=letsencrypt"

  # MongoDB 数据库 - 主数据库
  mongodb:
    image: mongo:6.0
    container_name: fm-mongodb
    restart: unless-stopped
    ports:
      - "27017:27017"
    environment:
      - MONGO_INITDB_ROOT_USERNAME=${MONGO_ROOT_USERNAME}
      - MONGO_INITDB_ROOT_PASSWORD=${MONGO_ROOT_PASSWORD}
      - MONGO_INITDB_DATABASE=football_manager_auth
    volumes:
      - mongodb-data:/data/db
      - mongodb-config:/data/configdb
      - ./mongo-init.js:/docker-entrypoint-initdb.d/mongo-init.js:ro
      - ./mongodb.conf:/etc/mongod.conf:ro
    networks:
      - football-manager-network
    command: mongod --config /etc/mongod.conf
    healthcheck:
      test: echo 'db.runCommand("ping").ok' | mongosh localhost:27017/test --quiet
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 2G
        reservations:
          cpus: '1.0'
          memory: 1G

  # MongoDB 副本集 - 数据备份
  mongodb-replica:
    image: mongo:6.0
    container_name: fm-mongodb-replica
    restart: unless-stopped
    environment:
      - MONGO_INITDB_ROOT_USERNAME=${MONGO_ROOT_USERNAME}
      - MONGO_INITDB_ROOT_PASSWORD=${MONGO_ROOT_PASSWORD}
    volumes:
      - mongodb-replica-data:/data/db
    networks:
      - football-manager-network
    command: mongod --replSet rs0 --bind_ip_all
    depends_on:
      - mongodb

  # Redis 缓存 - 主缓存
  redis:
    image: redis:7-alpine
    container_name: fm-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    environment:
      - REDIS_PASSWORD=${REDIS_PASSWORD}
    volumes:
      - redis-data:/data
      - ./redis.conf:/usr/local/etc/redis/redis.conf:ro
    networks:
      - football-manager-network
    command: redis-server /usr/local/etc/redis/redis.conf --requirepass ${REDIS_PASSWORD}
    healthcheck:
      test: ["CMD", "redis-cli", "--no-auth-warning", "-a", "${REDIS_PASSWORD}", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 512M
        reservations:
          cpus: '0.25'
          memory: 256M

  # Redis Sentinel - 高可用
  redis-sentinel:
    image: redis:7-alpine
    container_name: fm-redis-sentinel
    restart: unless-stopped
    volumes:
      - ./sentinel.conf:/usr/local/etc/redis/sentinel.conf:ro
    networks:
      - football-manager-network
    command: redis-sentinel /usr/local/etc/redis/sentinel.conf
    depends_on:
      - redis

  # Nginx 反向代理
  nginx:
    image: nginx:alpine
    container_name: fm-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/sites-enabled:/etc/nginx/sites-enabled:ro
      - ./ssl:/etc/nginx/ssl:ro
      - nginx-logs:/var/log/nginx
    networks:
      - football-manager-network
    depends_on:
      - auth-service
    healthcheck:
      test: ["CMD", "nginx", "-t"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Traefik 负载均衡器
  traefik:
    image: traefik:v3.0
    container_name: fm-traefik
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
      - "8080:8080"
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - ./traefik.yml:/etc/traefik/traefik.yml:ro
      - traefik-data:/data
    networks:
      - football-manager-network
    environment:
      - CLOUDFLARE_EMAIL=${CLOUDFLARE_EMAIL}
      - CLOUDFLARE_API_KEY=${CLOUDFLARE_API_KEY}

  # Prometheus 监控
  prometheus:
    image: prom/prometheus:latest
    container_name: fm-prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus-data:/prometheus
    networks:
      - football-manager-network
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'

  # Grafana 可视化
  grafana:
    image: grafana/grafana:latest
    container_name: fm-grafana
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD}
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana-data:/var/lib/grafana
      - ./grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./grafana/datasources:/etc/grafana/provisioning/datasources:ro
    networks:
      - football-manager-network
    depends_on:
      - prometheus

  # ELK Stack - Elasticsearch
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.8.0
    container_name: fm-elasticsearch
    restart: unless-stopped
    environment:
      - discovery.type=single-node
      - ES_JAVA_OPTS=-Xms1g -Xmx1g
      - xpack.security.enabled=false
    volumes:
      - elasticsearch-data:/usr/share/elasticsearch/data
    networks:
      - football-manager-network
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 2G
        reservations:
          cpus: '1.0'
          memory: 1G

  # ELK Stack - Logstash
  logstash:
    image: docker.elastic.co/logstash/logstash:8.8.0
    container_name: fm-logstash
    restart: unless-stopped
    volumes:
      - ./logstash.conf:/usr/share/logstash/pipeline/logstash.conf:ro
      - auth-logs:/var/log/auth:ro
    networks:
      - football-manager-network
    depends_on:
      - elasticsearch

  # ELK Stack - Kibana
  kibana:
    image: docker.elastic.co/kibana/kibana:8.8.0
    container_name: fm-kibana
    restart: unless-stopped
    ports:
      - "5601:5601"
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
    networks:
      - football-manager-network
    depends_on:
      - elasticsearch

  # 数据库备份服务
  mongodb-backup:
    image: alpine:latest
    container_name: fm-mongodb-backup
    restart: unless-stopped
    volumes:
      - mongodb-backups:/backups
      - ./backup-script.sh:/backup-script.sh:ro
    networks:
      - football-manager-network
    environment:
      - MONGO_HOST=mongodb
      - MONGO_USERNAME=${MONGO_ROOT_USERNAME}
      - MONGO_PASSWORD=${MONGO_ROOT_PASSWORD}
      - BACKUP_SCHEDULE=0 2 * * *  # 每天凌晨2点备份
    command: sh -c "apk add --no-cache mongodb-tools && crond -f"
    depends_on:
      - mongodb

  # 安全扫描服务
  security-scanner:
    image: owasp/zap2docker-stable
    container_name: fm-security-scanner
    volumes:
      - ./security-reports:/zap/wrk:rw
    networks:
      - football-manager-network
    command: zap-baseline.py -t http://auth-service:3001 -J security-report.json
    depends_on:
      - auth-service
    profiles:
      - security

  # 性能测试服务
  performance-tester:
    image: loadimpact/k6:latest
    container_name: fm-performance-tester
    volumes:
      - ./k6-scripts:/scripts:ro
      - ./performance-reports:/reports:rw
    networks:
      - football-manager-network
    command: run --out json=/reports/performance-report.json /scripts/load-test.js
    depends_on:
      - auth-service
    profiles:
      - performance

  # 健康检查服务
  healthcheck:
    image: alpine:latest
    container_name: fm-healthcheck
    restart: unless-stopped
    volumes:
      - ./healthcheck.sh:/healthcheck.sh:ro
    networks:
      - football-manager-network
    command: sh -c "apk add --no-cache curl && watch -n 30 /healthcheck.sh"
    depends_on:
      - auth-service

# 网络配置
networks:
  football-manager-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
    driver_opts:
      com.docker.network.bridge.name: fm-bridge

# 数据卷配置
volumes:
  # 应用数据
  mongodb-data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /opt/football-manager/data/mongodb
  mongodb-replica-data:
    driver: local
  mongodb-config:
    driver: local
  redis-data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /opt/football-manager/data/redis
  
  # 日志数据
  auth-logs:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /opt/football-manager/logs/auth
  nginx-logs:
    driver: local
  
  # 监控数据
  prometheus-data:
    driver: local
  grafana-data:
    driver: local
  elasticsearch-data:
    driver: local
  
  # 备份数据
  mongodb-backups:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /opt/football-manager/backups/mongodb
  
  # 其他数据
  traefik-data:
    driver: local
  auth-uploads:
    driver: local
