/**
 * TOTP多因子认证设置示例
 * 
 * 演示如何设置和使用TOTP多因子认证，包括：
 * - 生成TOTP密钥和QR码
 * - 启用MFA
 * - 禁用MFA
 * - 备用码管理
 */

const authClient = require('../basic-usage/auth-client');
const axios = require('axios');

/**
 * MFA客户端扩展
 */
class MfaClient {
  constructor(baseURL = 'http://localhost:3001') {
    this.baseURL = baseURL;
    this.authClient = authClient;
  }

  /**
   * 获取MFA状态
   */
  async getMfaStatus() {
    try {
      const response = await this.authClient.api.get('/auth/mfa/status');
      return response.data;
    } catch (error) {
      console.error('❌ 获取MFA状态失败:', error.response?.data?.message || error.message);
      throw error;
    }
  }

  /**
   * 生成MFA设置信息
   */
  async setupMfa() {
    try {
      const response = await this.authClient.api.post('/auth/mfa/setup');
      return response.data;
    } catch (error) {
      console.error('❌ 生成MFA设置失败:', error.response?.data?.message || error.message);
      throw error;
    }
  }

  /**
   * 启用MFA
   */
  async enableMfa(secret, code) {
    try {
      const response = await this.authClient.api.post('/auth/mfa/enable', {
        secret,
        code,
      });
      return response.data;
    } catch (error) {
      console.error('❌ 启用MFA失败:', error.response?.data?.message || error.message);
      throw error;
    }
  }

  /**
   * 禁用MFA
   */
  async disableMfa(password, code) {
    try {
      const response = await this.authClient.api.delete('/auth/mfa/disable', {
        data: {
          password,
          code,
        },
      });
      return response.data;
    } catch (error) {
      console.error('❌ 禁用MFA失败:', error.response?.data?.message || error.message);
      throw error;
    }
  }

  /**
   * 重新生成备用码
   */
  async regenerateBackupCodes() {
    try {
      const response = await this.authClient.api.post('/auth/mfa/backup-codes/regenerate');
      return response.data;
    } catch (error) {
      console.error('❌ 重新生成备用码失败:', error.response?.data?.message || error.message);
      throw error;
    }
  }
}

/**
 * MFA状态检查示例
 */
async function checkMfaStatus() {
  console.log('\n🔍 检查MFA状态');
  console.log('='.repeat(40));

  const mfaClient = new MfaClient();

  try {
    // 确保已登录
    if (!authClient.isAuthenticated()) {
      await authClient.login({
        identifier: 'admin',
        password: 'Admin123!@#',
      });
      console.log('✅ 已登录');
    }

    const status = await mfaClient.getMfaStatus();
    
    console.log('📊 MFA状态信息:');
    console.log(`  启用状态: ${status.data.enabled ? '已启用' : '未启用'}`);
    console.log(`  备用码数量: ${status.data.backupCodesCount}`);
    console.log(`  信任设备数量: ${status.data.trustedDevicesCount}`);
    
    return status.data;
  } catch (error) {
    console.error('❌ 检查MFA状态失败:', error.message);
    throw error;
  }
}

/**
 * TOTP设置流程示例
 */
async function totpSetupFlow() {
  console.log('\n🔐 TOTP设置流程');
  console.log('='.repeat(40));

  const mfaClient = new MfaClient();

  try {
    // 1. 生成TOTP密钥和QR码
    console.log('1️⃣ 生成TOTP密钥...');
    const setupData = await mfaClient.setupMfa();
    
    console.log('✅ TOTP设置信息生成成功:');
    console.log(`  密钥: ${setupData.data.secret}`);
    console.log(`  手动输入密钥: ${setupData.data.manualEntryKey}`);
    console.log(`  QR码URL: ${setupData.data.qrCodeUrl.substring(0, 50)}...`);
    console.log(`  备用码数量: ${setupData.data.backupCodes.length}`);
    
    // 显示备用码
    console.log('\n🔑 备用码列表:');
    setupData.data.backupCodes.forEach((code, index) => {
      console.log(`  ${index + 1}. ${code}`);
    });

    // 2. 模拟用户扫描QR码并输入验证码
    console.log('\n2️⃣ 模拟验证码验证...');
    
    // 注意：在实际应用中，用户需要使用认证器应用扫描QR码
    // 这里我们模拟一个验证码（实际应该从认证器应用获取）
    console.log('📱 请使用认证器应用扫描QR码，然后输入6位验证码');
    console.log('💡 在实际应用中，这里应该等待用户输入验证码');
    
    // 模拟验证码（实际应用中不应该这样做）
    const mockCode = '123456'; // 这只是示例，实际需要真实的TOTP代码
    
    console.log(`🔢 模拟验证码: ${mockCode}`);
    console.log('⚠️ 注意：这是模拟代码，实际使用时需要真实的TOTP验证码');

    return {
      secret: setupData.data.secret,
      backupCodes: setupData.data.backupCodes,
      qrCodeUrl: setupData.data.qrCodeUrl,
      mockCode,
    };
  } catch (error) {
    console.error('❌ TOTP设置失败:', error.message);
    throw error;
  }
}

/**
 * 启用MFA示例
 */
async function enableMfaExample() {
  console.log('\n✅ 启用MFA示例');
  console.log('='.repeat(40));

  const mfaClient = new MfaClient();

  try {
    // 首先获取设置信息
    const setupData = await totpSetupFlow();
    
    console.log('\n3️⃣ 启用MFA...');
    
    // 注意：在实际应用中，这里应该使用真实的TOTP验证码
    // 这里使用模拟代码仅用于演示
    try {
      const result = await mfaClient.enableMfa(setupData.secret, setupData.mockCode);
      
      console.log('✅ MFA启用成功!');
      console.log(`  状态: ${result.data.enabled ? '已启用' : '未启用'}`);
      console.log(`  新备用码数量: ${result.data.backupCodes.length}`);
      
      return result.data;
    } catch (error) {
      if (error.message.includes('无效的验证码')) {
        console.log('⚠️ 验证码无效（这是预期的，因为使用了模拟代码）');
        console.log('💡 在实际应用中，请使用认证器应用生成的真实验证码');
      } else {
        throw error;
      }
    }
  } catch (error) {
    console.error('❌ 启用MFA失败:', error.message);
    throw error;
  }
}

/**
 * 禁用MFA示例
 */
async function disableMfaExample() {
  console.log('\n❌ 禁用MFA示例');
  console.log('='.repeat(40));

  const mfaClient = new MfaClient();

  try {
    // 检查当前MFA状态
    const status = await mfaClient.getMfaStatus();
    
    if (!status.data.enabled) {
      console.log('ℹ️ MFA当前未启用，无需禁用');
      return;
    }

    console.log('🔓 禁用MFA...');
    
    // 注意：需要当前密码和MFA验证码
    const password = 'Admin123!@#';
    const mockCode = '123456'; // 实际应该使用真实的TOTP验证码或备用码
    
    try {
      await mfaClient.disableMfa(password, mockCode);
      console.log('✅ MFA禁用成功!');
    } catch (error) {
      if (error.message.includes('无效的验证码')) {
        console.log('⚠️ 验证码无效（这是预期的，因为使用了模拟代码）');
        console.log('💡 在实际应用中，请使用认证器应用生成的真实验证码或备用码');
      } else {
        throw error;
      }
    }
  } catch (error) {
    console.error('❌ 禁用MFA失败:', error.message);
    throw error;
  }
}

/**
 * 备用码管理示例
 */
async function backupCodesManagement() {
  console.log('\n🔑 备用码管理示例');
  console.log('='.repeat(40));

  const mfaClient = new MfaClient();

  try {
    // 检查当前状态
    const status = await mfaClient.getMfaStatus();
    console.log(`当前备用码数量: ${status.data.backupCodesCount}`);

    if (status.data.enabled) {
      console.log('\n🔄 重新生成备用码...');
      
      try {
        const result = await mfaClient.regenerateBackupCodes();
        
        console.log('✅ 备用码重新生成成功!');
        console.log(`新备用码数量: ${result.data.length}`);
        
        console.log('\n🔑 新备用码列表:');
        result.data.forEach((code, index) => {
          console.log(`  ${index + 1}. ${code}`);
        });
        
        console.log('\n⚠️ 重要提醒:');
        console.log('  • 请将备用码保存在安全的地方');
        console.log('  • 每个备用码只能使用一次');
        console.log('  • 旧的备用码已失效');
        console.log('  • 建议打印或写下备用码');
        
      } catch (error) {
        console.error('❌ 重新生成备用码失败:', error.message);
      }
    } else {
      console.log('ℹ️ MFA未启用，无法管理备用码');
    }
  } catch (error) {
    console.error('❌ 备用码管理失败:', error.message);
    throw error;
  }
}

/**
 * MFA最佳实践示例
 */
async function mfaBestPractices() {
  console.log('\n💡 MFA最佳实践示例');
  console.log('='.repeat(40));

  console.log('🔐 MFA安全最佳实践:');
  console.log('');
  
  console.log('1️⃣ 设置阶段:');
  console.log('  • 使用可信的认证器应用（Google Authenticator、Authy等）');
  console.log('  • 在安全环境中扫描QR码');
  console.log('  • 妥善保存备用码');
  console.log('  • 测试验证码是否正常工作');
  
  console.log('\n2️⃣ 使用阶段:');
  console.log('  • 定期检查MFA状态');
  console.log('  • 不要在不安全的设备上输入验证码');
  console.log('  • 及时更新认证器应用');
  console.log('  • 监控异常登录活动');
  
  console.log('\n3️⃣ 备用码管理:');
  console.log('  • 将备用码存储在安全位置');
  console.log('  • 定期检查剩余备用码数量');
  console.log('  • 使用后及时重新生成');
  console.log('  • 不要在网络上传输备用码');
  
  console.log('\n4️⃣ 恢复计划:');
  console.log('  • 准备多种恢复方式');
  console.log('  • 保持联系信息更新');
  console.log('  • 了解账户恢复流程');
  console.log('  • 定期测试恢复流程');
  
  console.log('\n5️⃣ 安全监控:');
  console.log('  • 启用登录通知');
  console.log('  • 定期检查活跃会话');
  console.log('  • 监控MFA使用情况');
  console.log('  • 及时报告可疑活动');
}

/**
 * 运行所有TOTP设置示例
 */
async function runAllTotpExamples() {
  console.log('🚀 开始运行TOTP设置示例...\n');

  try {
    // 检查MFA状态
    await checkMfaStatus();
    
    // TOTP设置流程
    await totpSetupFlow();
    
    // 启用MFA
    await enableMfaExample();
    
    // 备用码管理
    await backupCodesManagement();
    
    // 禁用MFA
    await disableMfaExample();
    
    // 最佳实践
    await mfaBestPractices();
    
    console.log('\n🎉 所有TOTP设置示例运行完成!');
  } catch (error) {
    console.error('\n💥 示例运行出错:', error.message);
  }
}

// 如果直接运行此文件
if (require.main === module) {
  runAllTotpExamples();
}

module.exports = {
  MfaClient,
  checkMfaStatus,
  totpSetupFlow,
  enableMfaExample,
  disableMfaExample,
  backupCodesManagement,
  mfaBestPractices,
  runAllTotpExamples,
};
