# Schema数据安全优化规范指南

## 📋 **概述**

本文档总结了在足球经理游戏服务器项目中，关于Mongoose Schema数据安全优化的最佳实践和标准规范。这些规范旨在确保敏感信息不会意外泄露到客户端，同时保持代码的简洁性和可维护性。

## 🎯 **核心原则**

### **1. 统一的数据清理策略**
- ✅ **优先使用Schema级别的toJSON配置**：在数据源头统一处理
- ✅ **避免在多个地方重复实现相同逻辑**：减少维护成本
- ✅ **兼容缓存数据和Mongoose文档**：处理不同数据来源

### **2. 敏感信息分类**
- 🔒 **绝对敏感**：passwordHash, salt, sessionId, deviceId, jti
- 🔒 **安全敏感**：passwordHistory, backupCodes, loginAttempts, lockedUntil
- 🔒 **内部字段**：_id, __v, mustChangePassword, accountLocked

## 🔧 **标准实现方案**

### **方案一：Schema级别的toJSON配置（推荐）**

```typescript
// 在User Schema中配置toJSON方法
UserSchema.set('toJSON', {
  virtuals: true,
  transform: function(doc, ret) {
    // 🔧 删除Mongoose内部字段
    delete ret._id;
    delete ret.__v;
    
    // 🔧 删除绝对敏感字段
    delete ret.passwordHash;
    delete ret.salt;
    delete ret.sessionId;
    delete ret.deviceId;
    delete ret.jti;
    
    // 🔧 删除security对象中的敏感信息
    if (ret.security) {
      delete ret.security.passwordHistory;
      delete ret.security.backupCodes;
      delete ret.security.loginAttempts;
      delete ret.security.lockedUntil;
      delete ret.security.mustChangePassword;
      delete ret.security.accountLocked;
    }
    
    return ret;
  }
});
```

### **方案二：缓存兼容性处理**

```typescript
// 在Controller中处理缓存数据和Mongoose文档的兼容性
async getProfile(@CurrentUser() user: UserDocument): Promise<ApiResponseDto<any>> {
  const userProfile = await this.usersService.findById(user.id);

  // 🔧 兼容缓存数据和Mongoose文档
  let userResponse: any;
  if (userProfile && typeof userProfile.toJSON === 'function') {
    // Mongoose文档：使用toJSON方法
    userResponse = userProfile.toJSON();
  } else {
    // 缓存数据：手动清理敏感信息
    userResponse = { ...userProfile };
    // 应用相同的清理逻辑
    this.cleanSensitiveData(userResponse);
  }

  return {
    success: true,
    data: userResponse,
    message: '获取用户信息成功',
    timestamp: new Date().toISOString(),
  };
}

private cleanSensitiveData(data: any): void {
  delete data.passwordHash;
  delete data.salt;
  delete data._id;
  delete data.__v;
  delete data.sessionId;
  delete data.deviceId;
  delete data.jti;
  
  if (data.security) {
    delete data.security.passwordHistory;
    delete data.security.backupCodes;
    delete data.security.loginAttempts;
    delete data.security.lockedUntil;
    delete data.security.mustChangePassword;
    delete data.security.accountLocked;
  }
}
```

## ❌ **反模式和避免的做法**

### **1. 过度设计的转换服务**
```typescript
// ❌ 避免：复杂的UserTransformerService
@Injectable()
export class UserTransformerService {
  // 200+行代码只为了删除几个字段
  toUserResponse(userDocument: UserDocument): UserResponseDto {
    // 复杂的类型检测和转换逻辑
    // 重复的敏感信息删除逻辑
  }
}
```

### **2. 手动构造用户数据**
```typescript
// ❌ 避免：在Service中手动构造用户数据
const { passwordHash, salt, security, ...safeUser } = user.toObject();
return {
  user: {
    ...safeUser,
    security: {
      mfaEnabled: security.mfaEnabled,
      trustedDevices: security.trustedDevices,
      lastPasswordChange: security.lastPasswordChange,
    }
  }
};
```

### **3. 多处重复的清理逻辑**
```typescript
// ❌ 避免：在多个Controller中重复相同的清理逻辑
delete userData.password;
delete userData.confirmPassword;
delete userData.gamePreferences;
```

## 🎯 **最佳实践总结**

### **1. 数据安全优先级**
1. **Schema级别配置** > Controller级别处理 > Service级别处理
2. **统一清理逻辑** > 分散清理逻辑
3. **自动化处理** > 手动处理

### **2. 缓存兼容性**
- ✅ **始终检查数据类型**：`typeof userProfile.toJSON === 'function'`
- ✅ **提供降级方案**：手动清理缓存数据
- ✅ **保持逻辑一致性**：相同的清理规则

### **3. 代码组织**
- ✅ **删除过度设计的转换服务**：如UserTransformerService
- ✅ **使用标准的Mongoose方法**：toJSON配置
- ✅ **保持代码简洁**：避免不必要的复杂性

## 🔍 **安全检查清单**

### **Schema配置检查**
- [ ] 是否配置了toJSON方法
- [ ] 是否删除了所有敏感字段
- [ ] 是否处理了嵌套对象中的敏感信息

### **Controller方法检查**
- [ ] 是否兼容缓存数据和Mongoose文档
- [ ] 是否有降级处理方案
- [ ] 是否避免了重复的清理逻辑

### **Service方法检查**
- [ ] 是否避免了手动构造用户数据
- [ ] 是否使用了统一的数据清理方法
- [ ] 是否删除了不必要的转换服务

## 📊 **性能优化建议**

### **1. 缓存策略**
- ✅ **缓存已清理的数据**：避免重复清理
- ✅ **使用合适的TTL**：平衡性能和数据新鲜度
- ✅ **监控缓存命中率**：优化缓存策略

### **2. 数据传输优化**
- ✅ **只传输必要字段**：减少网络开销
- ✅ **使用压缩**：对大型响应启用压缩
- ✅ **分页处理**：避免传输大量数据

## 🚀 **实施步骤**

### **第一步：Schema配置**
1. 在每个实体Schema中配置toJSON方法
2. 定义统一的敏感字段清理规则
3. 测试Schema配置的正确性

### **第二步：Controller优化**
1. 删除过度设计的转换服务
2. 实现缓存兼容性处理
3. 统一Controller的响应格式

### **第三步：测试验证**
1. 编写安全测试用例
2. 验证敏感信息不会泄露
3. 测试缓存和数据库数据的一致性

### **第四步：监控和维护**
1. 设置安全监控告警
2. 定期审查敏感字段清理规则
3. 更新文档和最佳实践

## 📝 **相关文件清单**

### **已优化的文件**
- `apps/auth/src/modules/user/entities/user.entity.ts` - Schema toJSON配置
- `apps/auth/src/modules/user/controllers/users.controller.ts` - 缓存兼容性处理
- `apps/auth/src/modules/auth/services/auth.service.ts` - 使用toJSON方法

### **已删除的文件**
- `apps/auth/src/modules/user/services/user-transformer.service.ts` - 过度设计的转换服务

### **需要关注的文件**
- 其他微服务的实体Schema配置
- 其他Controller的数据处理逻辑
- 缓存服务的数据存储格式

---

**文档版本**: v1.0  
**创建日期**: 2025-07-31  
**最后更新**: 2025-07-31  
**适用范围**: 足球经理游戏服务器项目
